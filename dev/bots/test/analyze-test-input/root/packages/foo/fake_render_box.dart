// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

abstract class RenderBox {
  void computeDryBaseline() {}
  void computeDryLayout() {}
  void computeDistanceToActualBaseline() {}
  void computeMaxIntrinsicHeight() {}
  void computeMinIntrinsicHeight() {}
  void computeMaxIntrinsicWidth() {}
  void computeMinIntrinsicWidth() {}
}
