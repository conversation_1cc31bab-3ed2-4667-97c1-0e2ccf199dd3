<!-- style overrides for dartdoc -->
<style>
@import 'https://fonts.googleapis.com/css?family=Roboto:500,400italic,300,400,100i';
@import 'https://fonts.googleapis.com/css?family=Google+Sans:500,400italic,300,400,100i';
@import 'https://fonts.googleapis.com/css?family=Open+Sans:500,400italic,300,400,100i';
@import 'https://fonts.googleapis.com/css?family=Material+Icons|Material+Icons+Outlined|Material+Icons+Sharp|Material+Icons+Round';
</style>

<link href="https://flutter.github.io/assets-for-api-docs/assets/cupertino/cupertino.css" rel="stylesheet" type="text/css">

<link href="../assets/overrides.css" rel="stylesheet" type="text/css">

<link href="https://fonts.googleapis.com/icon?family=Material+Icons|Material+Icons+Outlined|Material+Icons+Sharp|Material+Icons+Round" rel="stylesheet">
<style>
  /* Rule for sizing the icon. */
  .md-36 { font-size: 36px; }
</style>
