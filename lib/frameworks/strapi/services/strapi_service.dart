import 'dart:async';
import 'dart:collection';
import 'dart:convert' as convert;
import 'dart:convert';
import 'dart:developer';
import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fstore/common/logger.dart';
import 'package:fstore/models/settings_model.dart';
import 'package:fstore/models/vendor/vendor_model.dart';
import 'package:fstore/payment_gateway/paypal_payment_gateway.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:universal_html/html.dart' as html;

import '../../../common/config.dart';
import '../../../common/constants.dart';
import '../../../common/constants/local_keys.dart';
import '../../../data/boxes.dart';
import '../../../models/entities/blog.dart';
import '../../../models/entities/country_state.dart';
import '../../../models/index.dart'
    show
        AdditionalPaymentInfo,
        CartModel,
        Category,
        Country,
        Order,
        PaymentMethod,
        Product,
        ProductVariation,
        PromoCodeModel,
        ShippingMethod,
        User,
        UserModel;
import '../../../models/serializers/index.dart' show SerializerProduct;
import '../../../models/shipping_method_model.dart';
import '../../../models/webhook_model.dart';
import '../../../payment_gateway/fawaterak_payment_gateway.dart';
import '../../../screens/checkout/widgets/payment_method_item.dart';
import '../../../screens/checkout/widgets/payment_methods.dart';
import '../../../services/base_services.dart';
import '../../../services/firebase_service.dart';
import '../../../services/get_storage_service.dart';
import 'strapi_api.dart';

int? orderId;

List<Category> mainCategories = [];
ValueNotifier<Category?> selectedMainCategory = ValueNotifier(null);

String translatedText({required String? textEn, required String? textAr}) {
  final isEng = SettingsBox().languageCode == 'en';

  final nameEn = textEn == null || textEn.isEmpty ? textAr : textEn;

  final nameAr = textAr == null || textAr.isEmpty ? textEn : textAr;

  return (isEng ? nameEn : nameAr) ?? '';
}

class StrapiService extends BaseServices {
  StrapiService({required super.domain, super.blogDomain})
      : strapiAPI = StrapiAPI(domain);

  final StrapiAPI strapiAPI;
  List<Category>? cats;
  List<Map<String, dynamic>>? productOptions;
  List<Map<String, dynamic>>? productOptionValues;
  String? idLang;
  Map<String, dynamic>? configCache;

  @override
  Future<List<Category>> getMainCategories() async {
    try {
      var list = <Category>[];
      var response =
          await strapiAPI.getAsync('/main-categories', populateLevel: 3);

      for (var item in response) {
        list.add(Category.fromJsonStrapi(item, strapiAPI.apiLink));
      }

      mainCategories = list;

      return list;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<List<Category>> getCategories({lang}) async {
    try {
      var list = <Category>[];
      var response =
          await strapiAPI.getAsync('/product-categories?sort[0]=sort:asc');

      for (var item in response) {
        list.add(Category.fromJsonStrapi(item, strapiAPI.apiLink));
      }
      return list;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<List<Product>> getProducts({userId}) async {
    try {
      var productList = <Product>[];
      var response = await strapiAPI.getAsync('/products?is_active=true');

      for (var json in response) {
        var model = SerializerProduct.fromJson(json);

        productList.add(Product.fromJsonStrapi(model, strapiAPI.apiLink));
      }

      return productList;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>?> getHomeCache(String? lang) async {
    try {
      if (kAdvanceConfig.isCaching) {
        // var categoryList = <String?, Category>{};

        // final categories = await getCategories(lang: lang);
        //
        // for (var cat in categories) {
        //   categoryList[cat.id] = cat;
        // }

        final appJson = await rootBundle.loadString(kAppConfig);

        var config = convert.jsonDecode(appJson);

        if (config['HorizonLayout'] != null) {
          var horizontalLayout = config['HorizonLayout'] as List;
          List<dynamic>? items = [];

          for (var i = 0; i < horizontalLayout.length; i++) {
            //TODO-fixCheck
            // var categoryID = horizontalLayout[i]['category'].toString();
            // config['HorizonLayout'][i]['data'] =
            //     categoryList[categoryID] != null
            //         ? categoryList[categoryID]!.products
            //         : [];
            //
            // items = horizontalLayout[i]['items'];
            // if (items != null && items.isNotEmpty) {
            //   for (var j = 0; j < items.length; j++) {
            //     var categoryID = items[j]['category'].toString();
            //     items[j]['data'] = categoryList[categoryID]?.products;
            //   }
            // }
            horizontalLayout[i]['items'] = items;
          }

          //TODO-fixCheck
          // if (config['VerticalLayout'] != null) {
          //   var vCategory = config['VerticalLayout']['category'].toString();
          //   config['VerticalLayout']['data'] =
          //       categoryList[vCategory]?.products;
          // }
          configCache = config;
          return config;
        }
      }
      return configCache;
    } catch (e, trace) {
      printError(e, trace);
      //This error exception is about your Rest API is not config correctly so that not return the correct JSON format, please double check the document from this link https://docs.inspireui.com/fluxstore/woocommerce-setup/
      return null;
    }
  }

  @override
  Future<List<Product>?> fetchProductsLayout(
      {required config, lang, userId, bool refreshCache = false}) async {
    try {
      /// If enable Caching we should find the layout config inside configCache
      if (config.containsKey('page') &&
          int.parse(config['page'].toString()) > 1) return [];
      if (kAdvanceConfig.isCaching && configCache != null) {
        var obj;
        final horizontalLayout = configCache!['HorizonLayout'] as List?;
        if (horizontalLayout != null) {
          obj = horizontalLayout.firstWhere(
              (o) =>
                  o['layout'] == config['layout'] &&
                  ((o['category'] != null &&
                          o['category'] == config['category']) ||
                      (o['tag'] != null && o['tag'] == config['tag'])),
              orElse: () => null);
          if (obj != null && obj['data'].length > 0) return obj['data'];
        }

        final verticalLayout = configCache!['VerticalLayout'];
        if (verticalLayout != null &&
            verticalLayout['layout'] == config['layout'] &&
            ((verticalLayout['category'] != null &&
                    verticalLayout['category'] == config['category']) ||
                (verticalLayout['tag'] != null &&
                    verticalLayout['tag'] == config['tag']))) {
          return verticalLayout['data'];
        }
      }

      var productList = <Product>[];
      var endPoint = '/products?is_active=true';
      if (config['category'] != null) {
        endPoint += '&product_categories=${config['category']}';
      }
      var response = await strapiAPI.getAsync(endPoint);

      if (response != null) {
        for (var item in response) {
          var model = SerializerProduct.fromJson(item);
          final product = Product.fromJsonStrapi(model, strapiAPI.apiLink);

          productList.add(product);
        }
      }
      return productList;
    } catch (e, trace) {
      printLog(e.toString());
      printLog(trace.toString());
      return null;
    }
  }

  @override
  String getOrderByKey(orderBy) {
    switch (orderBy) {
      case 'price':
        return 'sale_price';
      // return 'price';
      case 'title':
        return 'title';
      case 'popularity':
      case 'rating':
        return 'review';
      case 'date':
      default:
        return 'createdAt';
    }
  }

  @override
  String getOrderDirection(order) {
    switch (order) {
      case 'asc':
        return ':asc';
      case 'desc':
      default:
        return ':desc';
    }
  }

  //TODO-Filter-getProducts
  @override
  Future<List<Product>?> fetchProductsByCategory({
    categoryId,
    tagId,
    page,
    minPrice,
    maxPrice,
    orderBy,
    lang,
    order,
    featured,
    onSale,
    attribute,
    attributeTerm,
    listingLocation,
    userId,
    String? include,
    String? search,
    bool? productType,
    nextCursor,
  }) async {
    try {
      var list = <Product>[];

      // Adjusted Strapi v5 endpoint
      var endPoint = '/products?filters[is_active]=true';

      if (categoryId != null && categoryId.toString().isNotEmpty) {
        endPoint += '&filters[categories][\$eq]=$categoryId';
      }
      if (search != null && search.isNotEmpty) {
        endPoint +=
            '&filters[\$or][0][title][\$containsi]=$search&filters[\$or][1][title_ar][\$containsi]=$search';
      }
      // if (search != null && search.isNotEmpty) {
      //   endPoint += '&filters[title][\$contains]=$search';
      // }
      if (orderBy != null) {
        endPoint +=
            '&sort[0]=${getOrderByKey(orderBy)}${getOrderDirection(order)}';
      }
      if (minPrice != null) {
        endPoint += '&filters[sale_price][\$gte]=$minPrice';
      }
      if (maxPrice != null) {
        endPoint += '&filters[sale_price][\$lte]=$maxPrice';
      }

      final filteredEndPoint = endPoint.replaceAll('?&', '?');

      var response = await strapiAPI.getAsync(filteredEndPoint);

      if (response != null) {
        for (var item in response) {
          var model = SerializerProduct.fromJson(item);
          list.add(Product.fromJsonStrapi(model, strapiAPI.apiLink));
        }
      }

      return list;
    } catch (e) {
      rethrow;
    }
  }

  // @override
  // Future<List<Product>?> fetchProductsByCategory({
  //   categoryId,
  //   tagId,
  //   page,
  //   minPrice,
  //   maxPrice,
  //   orderBy,
  //   lang,
  //   order,
  //   featured,
  //   onSale,
  //   attribute,
  //   attributeTerm,
  //   listingLocation,
  //   userId,
  //   String? include,
  //   String? search,
  //   bool? productType,
  //   nextCursor,
  // }) async {
  //   try {
  //     var list = <Product>[];
  //
  //     var endPoint = '/products?is_active=true';
  //
  //     if (categoryId != null && categoryId.toString().isNotEmpty) {
  //       endPoint += '&product_categories=$categoryId';
  //     }
  //     if (search != null) {
  //       endPoint += '&title_contains=$search';
  //     }
  //     if (orderBy != null) {
  //       endPoint +=
  //           '&_sort=${getOrderByKey(orderBy)}${getOrderDirection(order)}';
  //     }
  //     if (minPrice != null) {
  //       endPoint += '&sale_price_gte=$minPrice';
  //       // endPoint += '&price_gte=$minPrice';
  //     }
  //     if (maxPrice != null) {
  //       endPoint += '&sale_price_lte=$maxPrice';
  //       // endPoint += '&price_lte=$maxPrice';
  //     }
  //
  //     final filteredEndPoint = endPoint.replaceAll('?&', '?');
  //
  //     var response = await strapiAPI.getAsync(filteredEndPoint);
  //
  //     if (response != null) {
  //       for (var item in response) {
  //         var model = SerializerProduct.fromJson(item);
  //         list.add(Product.fromJsonStrapi(model, strapiAPI.apiLink));
  //       }
  //     }
  //
  //     return list;
  //   } catch (e) {
  //     rethrow;
  //   }
  // }

  static final Map<String, Product> productCache = {};

  @override
  Future<Product> getProduct(id, {lang, cursor}) async {
    // Check if the product is already in the cache
    if (productCache.containsKey(id)) {
      printLog('::::return cachedProduct $id');
      return productCache[id]!;
    }

    printLog('::::request getProduct $id');

    final isNumberOnlyTheID = RegExp(r'^\d+$').hasMatch(id);
    final url =
        isNumberOnlyTheID ? '/products?filters[id]=$id' : '/products/$id';

    var response = await strapiAPI.getAsync(url);

    printLog('asfasfaggegeges $response');

    if (response == null || response.isEmpty) return Product();

    var model = SerializerProduct.fromJson(response[0]);

    // Get selected size and color from local
    final product = Product.fromJsonStrapi(model, strapiAPI.apiLink);

    var items = UserBox().productsInCart;

    if (items != null && items.isNotEmpty) {
      for (var item in items) {
        final productItem = Product.fromLocalJson(item['product']);
        final ids = productItem.id.toString();

        final colorItem = item['color'];
        final sizeItem = item['size'];

        if (product.id == ids) {
          product.selectedSize = sizeItem;
          product.selectedColor = colorItem;
        }
      }
    }

    // Cache the product
    productCache[id] = product;

    return product;
  }

  // @override
  // Future<Product> getProduct(id, {lang, cursor}) async {
  //   printLog('::::request getProduct $id');
  //
  //   final isNumberOnlyTheID = RegExp(r'^\d+$').hasMatch(id);
  //
  //   final url =
  //       isNumberOnlyTheID ? '/products?filters[id]=$id' : '/products/$id';
  //
  //   var response = await strapiAPI.getAsync(url);
  //
  //   printLog('asfasfaggegeges $response');
  //
  //   if (response == null || response.isEmpty) return Product();
  //
  //   var model = SerializerProduct.fromJson(response[0]);
  //
  //   //? get selected size and color from local
  //
  //   final product = Product.fromJsonStrapi(model, strapiAPI.apiLink);
  //
  //   var items = UserBox().productsInCart;
  //
  //   if (items != null && items.isNotEmpty) {
  //     for (var item in items) {
  //       final productItem = Product.fromLocalJson(item['product']);
  //       final ids = productItem.id.toString();
  //
  //       final colorItem = item['color'];
  //       final sizeItem = item['size'];
  //
  //       if (product.id == ids) {
  //         product.selectedSize = sizeItem;
  //         product.selectedColor = colorItem;
  //       }
  //     }
  //   }
  //
  //   return product;
  // }

  @override
  Future<List<ProductVariation>?> getProductVariations(Product product,
      {String? lang = 'en'}) async {
    return null;
  }

  @override
  Future<List<ShippingMethod>> getShippingMethods(
      {CartModel? cartModel,
      String? token,
      String? checkoutId,
      String? langCode}) async {
    var lists = <ShippingMethod>[];
    var endPoint = '/shippings';

    var response = await strapiAPI.getAsync(
      endPoint,
      populateLevel: 3,
    );
    for (var item in response) {
      lists.add(ShippingMethod.fromStrapi(item));
    }
    return lists;
  }

  @override
  Future<List<PaymentMethod>> getPaymentMethods(
      {CartModel? cartModel,
      ShippingMethod? shippingMethod,
      String? token,
      String? langCode}) async {
    var lists = <PaymentMethod>[];
    var endPoint = '/payments';
    var response = await strapiAPI.getAsync(endPoint, ascSort: true);

    for (var item in response) {
      lists.add(PaymentMethod.fromStrapiJson(item));
    }
    return lists;
  }

  @override
  Future<PagingResponse<Order>> getMyOrders({
    User? user,
    cursor,
    String? cartId,
    String? orderStatus,
  }) async {
    var list = <Order>[];
    var response = await strapiAPI.getAsync('/orders?filters[user]=${user!.id}',
        populateLevel: 4);
    for (var item in response) {
      list.add(Order.fromJson(item));
    }
    return PagingResponse(data: list);
  }

  //getOrderByOrderId
  @override
  Future<Order> getOrderByOrderId({required orderId}) async {
    try {
      printLog('asfsaGGGGfaGGGG');
      final response = await http.get(
        Uri.parse('$apiURL/orders/$orderId?pLevel=3'),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseBody = jsonDecode(response.body);

        printLog('asfsaGGGGfaGGGG $responseBody');

        return Order.fromJson(responseBody['data']);
      } else {
        return Order();
      }
    } catch (e) {
      rethrow;
    }
  }

  //! Get Banners
  @override
  Future<List<(String name, String url)>>? getBanners() async {
    try {
      final response = await strapiAPI.getAsync('/banners');

      final bannersList = response as List?;

      final banners = bannersList
              ?.map((e) => (
                    (e['title'] ?? '') as String,
                    (e['media']['url'] ?? '') as String
                  ))
              .toList() ??
          [];

      return banners;
    } catch (e) {
      rethrow;
    }
  }

  //  Future<VendorModel?> getCurrentVendor() async {
  //     try {
  //       final response = await networkApiServices.getResponse(
  //         '${ApiEndPoints.vendors}/${VendorModelHelper.currentVendorId()}',
  //       );
  //       if (response == null) return null;
  //
  //       final vendorsData = VendorModel.fromJson(response);
  //
  //       return vendorsData;
  //     } on FetchDataException {
  //       rethrow;
  //     } on TimeoutException {
  //       rethrow;
  //     }
  //   }
  // get currentVendor data
  static Future<VendorModel?> getCurrentVendor(
      {bool withPopulate = true}) async {
    try {
      final populate = withPopulate ? '&pLevel' : '';

      final url =
          '$apiURL/vendors?filters[business_name]=$vendorBusinessName$populate';

      printLog('VendorURL $url');

      final response = await http.get(
        Uri.parse(url),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseBody = jsonDecode(response.body);

        printLog('VendorResponse $responseBody');

        final data = responseBody['data'] as List;

        if (data.isEmpty) return null;

        final vendorResponse = data.first;

        return VendorModel.fromJson(vendorResponse);
      } else {
        return null;
      }
    } catch (e) {
      rethrow;
    }
  }

  //CountryState
  @override
  Future<List<CountryState>> getCountryCities({
    required Country? country,
  }) async {
    try {
      const filterEndPointWithCountryId = '/shippings';

      var response = await strapiAPI.getAsync(
        filterEndPointWithCountryId,
        populateLevel: 4,
      );

      if (response == null || response.isEmpty) return [];

      final shippingResponse = response as List?;

      if (shippingResponse == null) return [];

      var cities = <CountryState>[];

      var item = shippingResponse.firstOrNull;

      if (item == null) return [];

      final citiesCost = item['cities_cost'] as List;

      for (var city in citiesCost) {
        final cityState = CountryState(
          id: city['city'] != null && city['city']['id'] != null
              ? city['city']['id'].toString()
              : '',
          name: city['city'] != null && city['city']['name'] != null
              ? city['city']['name']
              : '',
          cost: city['cost'],
          cityBoundaries:
              city['city'] != null && city['city']['boundaries'] != null
                  ? city['city']['boundaries']
                      .map<LatLng>((e) => LatLng(
                          double.parse(e['lat'].toString()),
                          double.parse(e['lng'].toString())))
                      .toList()
                  : [],
          cityLocation: city['city'] != null &&
                  city['city']['lat'] != null &&
                  city['city']['lng'] != null
              ? LatLng(
                  double.parse(city['city']['lat'].toString()),
                  double.parse(city['city']['lng'].toString()),
                )
              : null,
          areas: city['areas'] != null && city['areas'] is List
              ? (city['areas'] as List)
                  .map((e) => CountryState.fromStrapiJson(e))
                  .toList()
              : [],
        );

        if (cityState.cost == null || cityState.cost == 0) continue;

        cities.add(cityState);
      }

      return cities;
    } catch (e) {
      rethrow;
    }
  }

  // get promo codes PromoCodeModel and make sure active is true from url and vendor filter too
  static Future<List<PromoCodeModel>> getPromoCodes() async {
    try {
      final url =
          '$apiURL/promo-codes?filters[active]=true&filters[vendor][business_name]=$vendorBusinessName';

      printLog('PromoCodesUrl $url');

      final response = await http.get(
        Uri.parse(url),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseBody = jsonDecode(response.body);

        printLog('VendorResponse $responseBody');

        final data = responseBody['data'] as List;

        if (data.isEmpty) return [];

        promoCodesList = data.map((item) {
          return PromoCodeModel.fromJson(item);
        }).toList();

        return promoCodesList;
      } else {
        return [];
      }

      // if (response == null || response.isEmpty) return [];
      //
      // final promoCodeResponse = response as List?;
      //
      // if (promoCodeResponse == null) return [];
      //
      // var promoCodes = <PromoCodeModel>[];
      //
      // for (var item in promoCodeResponse) {
      //   promoCodes.add(PromoCodeModel.fromJson(item));
      // }
      //
      // return promoCodes;
    } catch (e) {
      rethrow;
    }
  }

  Future<http.MultipartFile?> _getPaymentScreenshotAttachment(
      CartModel cartModel) async {
    if (!isCOD(cartModel.paymentMethod?.title)) {
      if (kIsWeb) {
        final file = webAttachedPaymentScreenshot.value;
        if (file != null) {
          final reader = html.FileReader();
          reader.readAsArrayBuffer(file);
          await reader.onLoadEnd.first;

          return http.MultipartFile.fromBytes(
            'files',
            reader.result as List<int>,
            filename: file.name,
            contentType: MediaType('image', 'jpeg'),
          );
        }
      } else {
        final file = attachedPaymentScreenshot.value;
        if (file != null) {
          List<int> imageBytes = file.readAsBytesSync();
          return http.MultipartFile.fromBytes(
            'files',
            imageBytes,
            filename: 'payment_screenshot.jpg',
            contentType: MediaType('image', 'jpeg'),
          );
        }
      }
    }
    return null;
  }

  Future<Map<String, dynamic>> _prepareOrderData(
    CartModel cartModel, {
    required UserModel? user,
    required double totalPrice,
    int? invoiceId,
  }) async {
    final sizeAndColorAndQuantityBox = UserBox().selectedSizeAndColor;
    var idShipping = cartModel.shippingMethod?.id;
    var idUser = user?.user?.id;
    var idPayment = cartModel.paymentMethod?.id;

    final currentVendorId = (await getCurrentVendor(withPopulate: false))?.id;

    final mapLocationPicker = currentVendor?.config?.showMap == true &&
            GetStorageService.getData(key: LocalKeys.location) != null
        ? LatLng.fromJson(GetStorageService.getData(key: LocalKeys.location))
        : null;

    String? deviceFCMToken;

    try {
      deviceFCMToken = await FirebaseServices().messaging!.getToken();
    } catch (e) {
      printLog('Error_getting FCM token: $e');
    }

    return {
      'device_token': deviceFCMToken,
      'total': totalPrice,
      'discount': cartModel.getDiscountTotal(),
      'user': idUser,
      'shipping': idShipping,
      'payment': idPayment,
      if (mapLocationPicker != null)
        'location': {
          'lat': mapLocationPicker.latitude.toString(),
          'lng': mapLocationPicker.longitude.toString(),
        },
      if (selectedPromoCode != null) 'promo_code': selectedPromoCode?.id,
      'products_quantity': cartModel.productsInCart.keys.expand((item) {
        var product = cartModel.getProductById(Product.cleanProductID(item));
        var selectedSizeAndColorList =
            sizeAndColorAndQuantityBox[product?.id.toString()] ?? [];
        return selectedSizeAndColorList.map((selectedSizeAndColor) {
          return {
            'product': product?.id,
            'quantity': selectedSizeAndColor.quantity,
            'size': selectedSizeAndColor.size,
            'color': selectedSizeAndColor.color,
            'price': selectedSizeAndColor.price,
          };
        });
      }).toList(),
      'order_status': 'pending',
      'delivery_cost': ShippingMethodModel.selectedShippingMethodCost.value,
      'address': cartModel.address?.toOrderJson(),
      'phone_number': cartModel.address?.phoneNumber ?? '',
      'guest_name':
          '${cartModel.address?.firstName} ${cartModel.address?.lastName}',
      'vendor': currentVendorId,
      'note': cartModel.notes,
      if (invoiceId != null) 'invoice_id': invoiceId,
    };
  }

  Future<Order> _sendOrderRequest(
      {required Map<String, dynamic> data,
      http.MultipartFile? paymentScreenshotAttachment}) async {
    // final checkIfOrderExist = await _checkIfOrderIdAlreadyExist(orderId);

    // if (checkIfOrderExist) {
    //   throw 'You\'ve already made the order, Thank you.';
    // }

    data['order_id'] = orderId;
    printLog('asfsafsffsafsfsaf $data');

    var uri = Uri.parse('$apiURL/orders');

    int? paymentAttachmentId;

    if (paymentScreenshotAttachment != null) {
      paymentAttachmentId =
          await uploadFile(fileResult: paymentScreenshotAttachment);
      data['payment_attachment'] = paymentAttachmentId;
    }
    final response = await http.post(
      uri,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: jsonEncode({'data': data}),
    );

    if (response.statusCode == 200 || response.statusCode == 201) {
      var responseJson = jsonDecode(response.body);

      final docId = responseJson['data']['documentId'];

      await connectRelation(data: {
        'orders': {
          'connect': [docId],
        },
      });

      UserBox().clearSelectedSizeAndColor();
      attachedPaymentScreenshot.value = null;
      webAttachedPaymentScreenshot.value = null;

      printLog('FGGGGE $responseJson');

      try {
        return Order.fromCreateOrderJson(responseJson['data'],
            totalProducts: data['products_quantity'].length);
      } catch (e, trace) {
        Log.e('DDDGGGGGGDD$e$trace');

        return Order();
      }
    } else {
      var responseBody = jsonDecode(response.body);
      printLog('FailedRRRRRorder ${response.statusCode} $responseBody');
      throw Exception('Failed to create order');
    }
  }

  //! Upload multiple files
  Future<int?> uploadFile({
    required http.MultipartFile? fileResult,
  }) async {
    if (fileResult == null) return null;

    int? assetId;

    try {
      http.MultipartRequest request =
          http.MultipartRequest('POST', Uri.parse('$apiURL/upload'));

      // for (String? filePath in fileResult) {
      //   final fileExtension = filePath!.split('.').last;
      //   final mimeType = fileExtension == 'png' ? 'png' : 'jpeg';
      //
      //   request.files.add(await http.MultipartFile.fromPath(
      //     'files',
      //     filePath,
      //     contentType: MediaType('image', mimeType),
      //     filename: filePath.split('/').last,
      //   ));
      // }

      request.files.add(fileResult);

      request.headers.addAll({
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      });

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseJson =
            await jsonDecode(await response.stream.bytesToString());

        Log.f('UploadFilesResponse -------------------> $responseJson');

        assetId = (responseJson as List?)?.map(
          (asset) {
            return asset['id'] as int;
          },
        ).firstOrNull;

        Log.f('AssetIDS $assetId');
      } else {
        final body = await response.stream.bytesToString();

        throw 'FetchDataException: uploadFiles $body';
      }
    } catch (e, s) {
      throw 'FetchDataException: uploadFiles ${e.toString()} $s';
    }
    return assetId;
  }

  // Future<Order> _sendOrderRequest(
  //      {required Map<String, dynamic> data,
  //      http.MultipartFile? paymentScreenshotAttachment}) async {
  //    // final checkIfOrderExist = await _checkIfOrderIdAlreadyExist(orderId);
  //
  //    // if (checkIfOrderExist) {
  //    //   throw 'You\'ve already made the order, Thank you.';
  //    // }
  //
  //
  //    data['order_id'] = orderId;
  //
  //    var uri = Uri.parse('https://backend.idea2app.tech/api/orders');
  //    var request = http.MultipartRequest('POST', uri)
  //      ..fields.addAll({'data': jsonEncode(data)});
  //
  //    request.headers.addAll({
  //      'Content-Type': 'application/json',
  //      'Accept': 'application/json',
  //    });
  //    // if (paymentScreenshotAttachment != null) {
  //    //   request.files.add(paymentScreenshotAttachment);
  //    // }
  //
  //    var response = await request.send();
  //    if (response.statusCode == 200 || response.statusCode == 201) {
  //      var responseBody = await response.stream.bytesToString();
  //      var responseJson = jsonDecode(responseBody);
  //      printLog('DFDDDDDDDD $responseBody');
  //
  //      UserBox().clearSelectedSizeAndColor();
  //      attachedPaymentScreenshot.value = null;
  //      webAttachedPaymentScreenshot.value = null;
  //      return Order.fromJson(responseJson);
  //    } else {
  //      var responseBody = await response.stream.bytesToString();
  //      printLog('FailedRRRRRorder ${response.statusCode} $responseBody');
  //      throw Exception('Failed to create order');
  //    }
  //  }

  // Future<Map<String, dynamic>> _preparePaymentGateAwayData(
  //     CartModel cartModel, UserModel? user, double totalPrice) async {
  //   final customer = {
  //     'first_name': user?.user?.firstName,
  //     'last_name': user?.user?.lastName,
  //     'email': user?.user?.email,
  //     'phone': user?.user?.phoneNumber,
  //     'address': cartModel.address?.paymentAddress
  //   };
  //
  //   final cartItems = cartModel.productsInCart.keys.expand((item) {
  //     var product = cartModel.getProductById(Product.cleanProductID(item));
  //     var selectedSizeAndColorList =
  //         UserBox().selectedSizeAndColor[product?.id.toString()] ?? [];
  //
  //     return selectedSizeAndColorList.map((selectedSizeAndColor) {
  //       return {
  //         'name': product?.name,
  //         'quantity': selectedSizeAndColor.quantity,
  //         'price': product?.salePrice ?? product?.price,
  //       };
  //     });
  //   }).toList();
  //
  //   return {
  //     'cartItems': cartItems,
  //     'cartTotal': totalPrice,
  //     'shipping': ShippingMethodModel.selectedShippingMethodCost.value,
  //     'customer': customer,
  //     'currency': 'EGP',
  //     'payLoad': {},
  //     'sendEmail': true,
  //     'sendSMS': false,
  //   };
  // }
  //
  // Future<WebhookModel?> _createInvoiceLink(
  //     Map<String, dynamic> paymentData) async {
  //   // test name => Fawaterak test
  //   // test visa => ****************
  //   // expire date => 12/25
  //   // csv => 100
  //
  //   const fawaterakBaseURL =
  //       // kDebugMode == "dev" ?
  //       'https://staging.fawaterk.com';
  //   // 'https://app.fawaterk.com';
  //
  //   final response = await http.post(
  //     Uri.parse('$fawaterakBaseURL/api/v2/createInvoiceLink'),
  //     headers: {
  //       'Content-Type': 'application/json',
  //       'Authorization': 'Bearer ${Configurations.fawaterkApiKey}',
  //     },
  //     body: jsonEncode(paymentData),
  //   );
  //
  //   printLog('PaymentRes ${response.body}');
  //
  //   if (response.statusCode == 200 || response.statusCode == 201) {
  //     final responseBody = jsonDecode(response.body);
  //
  //     printLog('PaymentResSuccess $responseBody');
  //
  //     return WebhookModel.fromPaymentLinkJson(responseBody['data']);
  //   } else {
  //     throw 'Failed to create invoice link';
  //   }
  // }

  Future<Map<String, dynamic>> _preparePaymentGateAwayData(
      CartModel cartModel, UserModel? user, double totalPrice) async {
    final customer = {
      'first_name': user?.user == null
          ? cartModel.address?.firstName
          : user?.user?.firstName,
      'last_name': user?.user == null
          ? cartModel.address?.lastName
          : user?.user?.lastName,
      'phone': user?.user?.phoneNumber ?? cartModel.address?.lastName,
      'address': cartModel.address?.paymentAddress
    };

    final cartItems = cartModel.productsInCart.keys.expand((item) {
      var product = cartModel.getProductById(Product.cleanProductID(item));
      var selectedSizeAndColorList =
          UserBox().selectedSizeAndColor[product?.id.toString()] ?? [];
      return selectedSizeAndColorList.map((selectedSizeAndColor) {
        return {
          'name': product?.name,
          'quantity': selectedSizeAndColor.quantity,
          'price': product?.salePrice ?? product?.price,
        };
      });
    }).toList();

    return {
      'cartItems': cartItems,
      'cartTotal': totalPrice,
      'shipping': ShippingMethodModel.selectedShippingMethodCost.value,
      'customer': customer,
      'currency': 'EGP',
      'payLoad': {},
      'sendEmail': true,
      'sendSMS': false,
    };
  }

  Future<WebhookModel?> _createInvoiceLink(
    Map<String, dynamic> paymentData, {
    String? apiKey,
  }) async {
    // test name => Fawaterak test
    // test visa => ****************
    // expire date => 12/25
    // csv => 100

    printLog('APIKEY $apiKey');
    if (apiKey == null) {
      throw 'Failed to create invoice link';
    }

    const fawaterakBaseURL =
        // kDebugMode == "dev" ?
        'https://staging.fawaterk.com';
    // 'https://app.fawaterk.com';

    final response = await http.post(
      Uri.parse('$fawaterakBaseURL/api/v2/createInvoiceLink'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $apiKey',
        //Configurations.fawaterkApiKey
      },
      body: jsonEncode(paymentData),
    );

    printLog('PaymentRes ${response.body}');

    final responseBody = jsonDecode(response.body);

    if (response.statusCode == 200 || response.statusCode == 201) {
      printLog('PaymentResSuccess $responseBody');

      return WebhookModel.fromPaymentLinkJson(responseBody['data']);
    } else {
      throw 'Failed to create invoice link ${response.statusCode}\nBody${response.body}';
    }
  }

  @override
  Future<Order> createOrder(
    BuildContext context, {
    CartModel? cartModel,
    UserModel? user,
    bool? paid,
    AdditionalPaymentInfo? additionalPaymentInfo,
  }) async {
    try {
      //? Generate 6 orderId number
      orderId = math.Random().nextInt(999999);

      var total = cartModel!.getTotal();
      final totalPrice = (total ?? 0) - (cartModel.getShippingCost() ?? 0);

      if (totalPrice == 0) {
        throw 'Error occurred while making an order, Please try again.';
      }

      ////https://staging.fawaterk.com/transaction/success

      var paymentScreenshotAttachment =
          await _getPaymentScreenshotAttachment(cartModel);

      var data = <String, dynamic>{};

      var invoiceId;

      if (isOnlinePayment(cartModel.paymentMethod?.title)) {
        final paymentData =
            await _preparePaymentGateAwayData(cartModel, user, totalPrice);

        final invoiceLink = await _createInvoiceLink(
          paymentData,
          apiKey: cartModel.paymentMethod?.apiKey,
        );

        if (invoiceLink == null || invoiceLink.url == null) {
          throw 'Failed to create invoice link';
        }

        await showWebViewSheet(context,
            link: invoiceLink.url!, invoiceId: invoiceLink.invoiceId);

        final checkIfInvoiceExistInWebHookDB =
            await checkIfInvoiceExistInDBWebHook(invoiceLink.invoiceId);

        if (!checkIfInvoiceExistInWebHookDB) {
          throw 'Failed to make online payment !';
        }

        invoiceId = invoiceLink.invoiceId;
      }

      if (isPaypal(cartModel.paymentMethod?.title)) {
        await Navigator.of(context).push(MaterialPageRoute(
          builder: (context) => PaypalPaymentGatewayView(
            totalPrice: totalPrice,
            onSuccess: () async {
              data = await _prepareOrderData(
                cartModel,
                user: user,
                totalPrice: totalPrice,
                invoiceId: invoiceId,
              );

              printLog('Order_Data $data');

              final order = await _sendOrderRequest(
                data: data,
                paymentScreenshotAttachment: paymentScreenshotAttachment,
              );

              unawaited(Navigator.of(context).pushNamed(RouteList.orderdSuccess,
                  arguments: {'order': order}));
            },
          ),
        ));

        throw 'Failed to make online payment !';
      }

      data = await _prepareOrderData(
        cartModel,
        user: user,
        totalPrice: totalPrice,
        invoiceId: invoiceId,
      );

      printLog('DFFFFDF ${data}');

      // throw 'TEST';
      return await _sendOrderRequest(
        data: data,
        paymentScreenshotAttachment: paymentScreenshotAttachment,
      );
    } catch (e, trace) {
      printLog('OrderError ${e.toString()}\n${trace.toString()}');
      rethrow;
    }
  }

  // Future<bool> _checkIfOrderIdAlreadyExist(int? orderId) async {
  //   final response = await http.get(
  //     Uri.parse('$backendURL/orders?order_id=$orderId'),
  //   );
  //
  //   final responseBody = jsonDecode(response.body);
  //
  //   printLog('Order_RES $responseBody');
  //
  //   if (response.statusCode == 200 || response.statusCode == 201) {
  //     final responseBody = jsonDecode(response.body);
  //
  //     printLog('CheckIfOrderIdAlreadyExistBody $responseBody');
  //
  //     final responseData = responseBody as List?;
  //
  //     return responseBody != null && responseData!.isNotEmpty;
  //   } else {
  //     return false;
  //   }
  // }

  @override
  // update order with cancelled status
  Future<Order> cancelOrder({
    required Order? order,
    required UserModel user,
    String? userCookie,
  }) async {
    var data = {
      'order_status': 'canceled',
    };

    // var userJwtToken = user.user!.jwtToken;

    var body = json.encode({
      'data': data,
    });
    final response = await httpPut(
      '$apiURL/orders/${order?.documentId}'.toUri()!,
      headers: {
        'Content-Type': 'application/json',
        // 'Token': userJwtToken!,
      },
      body: body,
    );
    var responseBody = convert.jsonDecode(response.body);

    log('asfsafsaf ${responseBody}}');

    final orderData =
        getOrderByOrderId(orderId: responseBody['data']['documentId']);
    log('asfsafFDDDDDDDDsaf ${orderData}');

    return orderData;
  }

  @override
  Future<PagingResponse<Product>> searchProducts(
      {name,
      categoryId,
      categoryName,
      tag = '',
      attribute = '',
      attributeId = '',
      page,
      lang,
      listingLocation,
      userId}) async {
    try {
      var list = <Product>[];
      var response = await strapiAPI.getAsync(
          '/products?is_active=true&title_contains=$name&product_categories_contains=$categoryId');
      for (var item in response) {
        var model = SerializerProduct.fromJson(item);
        list.add(Product.fromJsonStrapi(model, strapiAPI.apiLink));
      }
      return PagingResponse(data: list);
    } catch (e) {
      rethrow;
    }
  }

  //  Future<SettingsModel> getSettings() async {
  //     try {
  //       final settings =
  //           await _networkApiServices.getResponse(ApiEndPoints.settings);
  //
  //       final settingsData = SettingsModel.fromJson(settings);
  //       return settingsData;
  //     } on FetchDataException {
  //       rethrow;
  //     } on TimeoutException {
  //       rethrow;
  //     }
  //   }

  // get settings
  static Future<SettingsModel> getSettings() async {
    try {
      final response = await http.get(
        Uri.parse('$apiURL/setting?pLevel=3'),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseBody = jsonDecode(response.body);

        return SettingsModel.fromJson(responseBody['data']);
      } else {
        return SettingsModel();
      }
    } catch (e) {
      rethrow;
    }
  }

  /// Create a New User
  @override
  Future<User> createUser({
    String? firstName,
    String? lastName,
    String? username,
    String? password,
    String? phoneNumber,
  }) async {
    try {
      var niceName = '${firstName!} ${lastName!}';
      // ignore: omit_local_variable_types
      Map data = {
        'displayName': niceName,
        'firstName': firstName,
        'lastName': lastName,
        'username': username,
        'phone': phoneNumber,
        'email': username,
        'password': password,
        'vendor': (await getCurrentVendor())?.id,
        // currentVendor?.id,
        'role': 2,
      };

      //encode Map to JSON
      var body = json.encode(data);

      final response = await httpPost('$apiURL/users'.toUri()!,
              // '$apiURL/auth/local/register'.toUri()!,
              headers: {'Content-Type': 'application/json'},
              body: body)
          .timeout(
        const Duration(seconds: 15),
      );

      var responseBody = convert.jsonDecode(response.body);

      final docId = responseBody['documentId'];

      await connectRelation(data: {
        'users': {
          'connect': [docId],
        },
      });

      var user;

      if (response.statusCode == 200 || response.statusCode == 201
          // &&
          // responseBody['jwt'] != null
          ) {
        printLog('FAFAGAGE $responseBody');
        user = User.fromStrapi(responseBody);
        printLog('USSSSSSS $user');

        // if (!kIsWeb) {
        //   await updateFCMToken(userId: user.id);
        // }
      } else {
        throw ('[Strapi] createUser fail');
      }
      return user;
    } catch (e, trace) {
      printLog(e.toString());
      printLog(trace.toString());
      rethrow;
    }
  }

  // updateUserInfo
  @override
  Future<Map<String, dynamic>?>? updateUserInfo(
    Map<String, dynamic> json,
    String? token,
  ) async {
    printLog('LddddLfffffLLL $json');

    try {
      var body = jsonEncode(json);

      printLog('222ewqewewwewe $json');

      final response = await httpPut(
        '$apiURL/users/${json['user_id']}'.toUri()!,
        headers: {
          'Content-Type': 'application/json',
          // 'Token': token ?? '',//TODO-Token
        },
        body: body,
      );
      printLog('************ $json');

      printLog('LLLLL ${response.body}');

      var responseBody = convert.jsonDecode(response.body);
      return responseBody;
    } catch (e, trace) {
      printLog('UpdateAccountERROR $e \n $trace');
      rethrow;
    }
  }

  /// login
  @override
  Future<User?> login({username, password}) async {
    try {
      // ignore: omit_local_variable_types
      Map data = {
        'identifier': username,
        'password': password,
      };
      var body = json.encode(data);
      final response = await httpPost('$apiURL/auth/local'.toUri()!,
          headers: {'Content-Type': 'application/json'}, body: body);
      var user;

      log('LoginBody -> ${response.body}');

      var responseBody = convert.jsonDecode(response.body);
      if ((response.statusCode == 200 || response.statusCode == 201) &&
          responseBody['jwt'] != null) {
        user = User.fromStrapi(responseBody);

        // if (!kIsWeb) {
        //   await updateFCMToken(userId: user.id);
        // }
      } else {
        throw Exception('The username or password is incorrect.');
      }
      return user;
    } catch (err, trace) {
      printLog(err.toString());
      printLog(trace.toString());
      rethrow;
    }
  }

  Future<void> connectRelation({required Map<String, dynamic> data}) async {
    final currentDocumentId = currentVendor?.documentId;
    final url = '$apiURL/vendors/$currentDocumentId'.toUri()!;

    Log.f('ConnectRelationURL $url');

    final res = await httpPut(url,
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'data': data,
        }));

    Log.f('ConnectRelationResponse ${res.body}');
  }

  //? Update Login User (Put) With FCM Token
  // Future<void> updateFCMToken({required String? userId}) async {
  //   try {
  //     final token = await NotificationService.getToken();
  //     // ignore: omit_local_variable_types
  //     Map data = {
  //       'device_token': token,
  //     };
  //
  //     var body = json.encode(data);
  //     final res = await httpPut('$backendURL/users/$userId'.toUri()!,
  //         headers: {'Content-Type': 'application/json'}, body: body);
  //
  //     log('fasafafs ${res.body}');
  //   } catch (err, trace) {
  //     printLog(err.toString());
  //     printLog(trace.toString());
  //     rethrow;
  //   }
  // }

  @override
  Future<PagingResponse<Blog>> getBlogs(dynamic cursor) async {
    return const PagingResponse();
    // try {
    //   final response = await httpGet('$backendURL/posts'.toUri()!);
    //   List data = jsonDecode(response.body);
    //   return PagingResponse(
    //       data: data.map((json) {
    //     return Blog.fromStrapiJson(json);
    //   }).toList());
    // } catch (e) {
    //   return const PagingResponse();
    // }
  }

// @override
// Future<Order?> cancelOrder({Order? order, String? userCookie}) async {
//   // Does not effect online because not implement
//   final newOrder =
//       await updateOrder(order!.id, status: 'cancelled', token: userCookie);
//   return newOrder;
// }
}
