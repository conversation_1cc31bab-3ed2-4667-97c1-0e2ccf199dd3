import 'package:flutter/material.dart';

class TranslateOnHover extends StatefulWidget {
  final Widget child;

  const TranslateOnHover({super.key, required this.child});

  @override
  _TranslateOnHoverState createState() => _TranslateOnHoverState();
}

class _TranslateOnHoverState extends State<TranslateOnHover> {
  final nonHoverTransform = Matrix4.identity();
  final hoverTransform = Matrix4.identity()..translate(0, -10, 0);

  bool _hovering = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onHover: (e) {
        setState(() => _hovering = true);
      },
      onExit: (e) {
        setState(() => _hovering = false);
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        transform: _hovering ? hoverTransform : nonHoverTransform,
        child: widget.child,
      ),
    );
  }
}

class ScaleOnHover extends StatefulWidget {
  final Widget child;

  const ScaleOnHover({super.key, required this.child});

  @override
  _ScaleOnHoverState createState() => _ScaleOnHoverState();
}

class _ScaleOnHoverState extends State<ScaleOnHover> {
  double scale = 1;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onHover: (e) {
        setState(() => scale = 1.04); // Increase scale to 1.1 when hovered
      },
      onExit: (e) {
        setState(() => scale = 1);
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        transform: Matrix4.identity()..scale(scale),
        child: widget.child,
      ),
    );
  }
}
