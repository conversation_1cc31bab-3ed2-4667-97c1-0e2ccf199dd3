import 'package:fstore/models/template_model.dart';

class SettingsModel {
  final String termsAr;
  final String termsEn;
  final String aboutUsAr;
  final String aboutUsEn;
  final List<TemplateModel>? templates;

  SettingsModel({
    this.termsAr = '',
    this.termsEn = '',
    this.aboutUsAr = '',
    this.aboutUsEn = '',
    this.templates,
  });

  factory SettingsModel.fromJson(Map<String, dynamic> json) {
    final templates = json['templates'] != null
        ? (json['templates'] as List)
            .map((template) => TemplateModel.fromJson(template))
            .toList()
        : null;

    return SettingsModel(
      termsAr: json['terms_ar'] ?? '',
      termsEn: json['terms_en'] ?? '',
      aboutUsAr: json['about_ar'] ?? '',
      aboutUsEn: json['about_en'] ?? '',
      templates: templates,
    );
  }

  factory SettingsModel.empty() => SettingsModel(
        termsAr: '',
        termsEn: '',
        aboutUsAr: '',
        aboutUsEn: '',
      );
}
