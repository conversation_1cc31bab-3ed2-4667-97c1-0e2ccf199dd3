import 'package:json_annotation/json_annotation.dart';

import 'feature_image.dart';
import 'product.dart';

part 'product_category.g.dart';

@JsonSerializable()
class SerializerProductCategory {
  int? id;
  String? name;
  String? nameAr;
  String? description;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'feature_image')
  FeatureImage? featureImage;

  List<SerializerProduct>? products;
  List<SerializerProductCategory>? subCategories;
  SerializerProductCategory? mainCategory;

  SerializerProductCategory(
    this.id,
    this.name,
    this.nameAr,
    this.description,
    this.featureImage,
    this.products,
    this.subCategories,
    this.mainCategory,
  );

  factory SerializerProductCategory.fromJson(Map<String, dynamic> json) {
    return _$SerializerProductCategoryFromJson(json);
  }

  Map<String, dynamic> toJson() => _$SerializerProductCategoryToJson(this);
}
