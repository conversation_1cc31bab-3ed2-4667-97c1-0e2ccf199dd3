// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product_category.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SerializerProductCategory _$SerializerProductCategoryFromJson(
        Map<String, dynamic> json) =>
    SerializerProductCategory(
      json['id'] as int?,
      json['name'] as String?,
      json['name_ar'] as String?,
      json['description'] as String?,
      json['feature_image'] == null
          ? null
          : FeatureImage.fromJson(json['feature_image']),
      (json['products'] as List<dynamic>?)
          ?.map((e) => SerializerProduct.fromJson(e as Map<String, dynamic>))
          .toList(),
      (json['categories'] as List<dynamic>?)
          ?.map((e) =>
              SerializerProductCategory.fromJson(e as Map<String, dynamic>))
          .toList(),
      json['main_category'] == null
          ? null
          : SerializerProductCategory.fromJson(
              json['main_category'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$SerializerProductCategoryToJson(
        SerializerProductCategory instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'name_ar': instance.nameAr,
      'description': instance.description,
      'feature_image': instance.featureImage?.toJson(),
      'products': instance.products?.map((e) => e.toJson()).toList(),
      'categories': instance.subCategories?.map((e) => e.toJson()).toList(),
      'main_category': instance.mainCategory?.toJson(),
    };
