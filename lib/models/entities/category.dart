import 'package:html_unescape/html_unescape.dart';
import 'package:quiver/strings.dart';

import '../../common/constants.dart';
import '../../frameworks/strapi/services/strapi_service.dart';
import '../serializers/product_category.dart';
import 'product.dart';

class Category {
  String? id;
  String? sku;
  String? name;
  String? image;
  String? parent;
  String? slug;
  int? totalProduct;
  List<Product>? products;
  bool? hasChildren = false;
  List<Category> subCategories = [];
  String? onlineStoreUrl;
  Category? mainCategory;

  int? _leveldepth;

  Category({
    this.id,
    this.sku,
    this.name,
    this.image,
    this.parent,
    this.slug,
    this.totalProduct,
    this.products,
    this.mainCategory,
    this.hasChildren = false,
    this.subCategories = const [],
  });

  String get displayName => name ?? 'Unknown';

  Category.fromJson(Map parsedJson) {
    if (parsedJson['slug'] == 'uncategorized') {
      return;
    }

    try {
      id = parsedJson['id']?.toString() ?? parsedJson['term_id'].toString();
      name = HtmlUnescape().convert(parsedJson['name']);
      parent = parsedJson['parent'].toString();
      totalProduct = parsedJson['count'];
      slug = parsedJson['slug'];
      final image = parsedJson['image'];
      if (image != null) {
        this.image = image['src'].toString();
      } else {
        this.image = kDefaultImage;
      }
      hasChildren = parsedJson['has_children'] ?? false;
    } catch (e, trace) {
      printLog(e.toString());
      printLog(trace.toString());
    }
  }

  Category copyWith({
    String? id,
    String? sku,
    String? name,
    String? image,
    String? parent,
    String? slug,
    Category? mainCategory,
    int? totalProduct,
    List<Product>? products,
    bool? hasChildren,
    List<Category>? subCategories,
  }) {
    return Category(
      id: id ?? this.id,
      sku: sku ?? this.sku,
      name: name ?? this.name,
      mainCategory: mainCategory ?? this.mainCategory,
      image: image ?? this.image,
      parent: parent ?? this.parent,
      slug: slug ?? this.slug,
      totalProduct: totalProduct ?? this.totalProduct,
      products: products ?? this.products,
      subCategories: subCategories ?? this.subCategories,
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'parent': parent,
        'image': {'src': image},
      };

  Category.fromJsonStrapi(Map<String, dynamic> parsedJson, Function apiLink) {
    try {
      var model = SerializerProductCategory.fromJson(parsedJson);

      if (model.featureImage != null) {
        image = model.featureImage!.url;
      } else {
        image = kDefaultImage;
      }

      mainCategory = model.mainCategory != null
          ? Category(
              id: model.mainCategory!.id.toString(),
              name: translatedText(
                textEn: model.mainCategory!.name,
                textAr: model.mainCategory!.nameAr,
              ),
              image: model.mainCategory!.featureImage?.url ?? kDefaultImage,
              parent: '0',
              totalProduct: model.mainCategory!.products?.length ?? 0,
            )
          : null;

      id = model.id.toString();
      name = translatedText(textEn: model.name, textAr: model.nameAr);

      parent = '0';
      totalProduct = model.products?.length ?? 0;

      products = [];
      for (var product in model.products ?? []) {
        var newProduct = Product.fromJsonStrapi(product, apiLink);
        products!.add(newProduct);
      }

      subCategories = [];
      if (model.subCategories != null) {
        for (var subCategory in model.subCategories!) {
          subCategories.add(Category(
            id: subCategory.id.toString(),
            name: translatedText(
              textEn: subCategory.name,
              textAr: subCategory.nameAr,
            ),
            image: subCategory.featureImage?.url ?? kDefaultImage,
            parent: id,
            totalProduct: subCategory.products?.length ?? 0,
          ));
        }
      }
    } catch (e, trace) {
      printLog(e.toString());
      printLog(trace.toString());
    }
  }

  bool get isRoot => parent == '0' || _leveldepth == 2;

  @override
  String toString() => 'Category { id: $id  name: $name}';

  static List<Category> parseCategoryList(response) {
    var categories = <Category>[];
    if (response is Map && isNotBlank(response['message'])) {
      throw Exception(response['message']);
    } else {
      for (var item in response) {
        if (!item['slug'].toString().contains('uncategorized')) {
          categories.add(Category.fromJson(item));
        }
      }
      return categories;
    }
  }
}
