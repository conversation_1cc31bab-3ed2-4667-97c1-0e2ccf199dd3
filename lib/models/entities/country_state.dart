import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../../common/constants.dart';
import 'country.dart';

class CountryState {
  String? id;
  String? code;
  String? name;
  String? nameAr;
  Country? country;
  List<LatLng> cityBoundaries = [];
  LatLng? cityLocation;

  CountryState? city;

  List<CountryState>? areas;

  bool? freeShipping;

  num? cost;

  CountryState({
    this.id,
    this.code,
    this.name,
    this.country,
    this.city,
    this.nameAr,
    this.cost,
    this.cityBoundaries = const [],
    this.cityLocation,
    this.areas = const [],
    this.freeShipping,
  });

  CountryState.fromConfig(dynamic parsedJson) {
    if (parsedJson is Map) {
      id = parsedJson['code'];
      code = parsedJson['code'];
      name = parsedJson['name'];
      nameAr = parsedJson['name_ar'];
      freeShipping = parsedJson['free_shipping'] ?? false;
    }
    if (parsedJson is String) {
      id = parsedJson;
      code = parsedJson;
      name = parsedJson;
      nameAr = parsedJson;
    }
  }

  CountryState.fromStrapiJson(Map parsedJson) {
    id = parsedJson['id'].toString();
    code = parsedJson['code'];
    name = parsedJson['name'];

    if (parsedJson['country'] != null) {
      country = parsedJson['country'].runtimeType == int
          ? Country(id: parsedJson['country'].toString())
          : Country.fromStrapiJson(parsedJson['country']);
    }

    printLog('asffffssssssgsg ${parsedJson['boundaries']}');

    if (parsedJson['boundaries'] != null) {
      cityBoundaries = (parsedJson['boundaries'] as List)
          .map((e) => LatLng(e['lat'], e['lng']))
          .toList();
    }

    if (parsedJson['lat'] != null && parsedJson['lng'] != null) {
      cityLocation = LatLng(double.parse(parsedJson['lat']!.toString()),
          double.parse(parsedJson['lng']!.toString()));
    }

    if (parsedJson['areas'] != null) {
      areas = (parsedJson['areas'] as List)
          .map((e) => CountryState.fromStrapiJson(e))
          .toList();
    }

    cost = parsedJson['cost'] != null
        ? num.tryParse(parsedJson['cost'].toString())
        : null;

    freeShipping = parsedJson['free_shipping'] ?? false;
  }

  @override
  String toString() {
    return 'CountryState{id: $id, code: $code, name: $name, country: $country, city: $city, cost: $cost, cityBoundaries: $cityBoundaries, cityLocation: $cityLocation}';
  }
}
