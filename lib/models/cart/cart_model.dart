import 'cart_base.dart';
import 'cart_model_strapi.dart';

export 'cart_base.dart';

class CartInject {
  static final CartInject _instance = CartInject._internal();

  factory CartInject() => _instance;

  CartInject._internal();

  /// init default CartModel
  CartModel model = CartModelStrapi();

  void init(config) {
    switch (config['type']) {
      // case 'magento':
      // model = CartModelMagento();
      // break;
      // case 'shopify':
      //   model = CartModelShopify();
      //   break;
      // case 'opencart':
      //   model = CartModelOpencart();
      //   break;
      // case 'presta':
      //   model = CartModelPresta();
      //   break;
      case 'strapi':
        model = CartModelStrapi();
        break;
      // case 'bigCommerce':
      //   model = CartModelBigCommerce();
      //   break;
      default:
        model = CartModelStrapi();
    }
    model.initData();
  }
}
