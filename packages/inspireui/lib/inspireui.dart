library;

/// Common Extension
export 'extensions/color_extension.dart';
export 'extensions/screen_extensions.dart';
export 'extensions/string_extension.dart';

/// Common Util Functions or Class
export 'utils/colors.dart';
export 'utils/date_time_utils.dart';
export 'utils/encode.dart';
export 'utils/event_bus.dart';
export 'utils/http_client.dart';
export 'utils/logs.dart';
export 'utils/screen_utils.dart';
export 'utils/video.dart';

/// Common Widgets
export 'widgets/app_icon.dart';
export 'widgets/appbar_widget/appbar_widget.dart';
export 'widgets/auto_hide_keyboard.dart';
export 'widgets/button_show_content_dialog_widget.dart';
export 'widgets/button_widget.dart';
export 'widgets/circle_button_text.dart';
export 'widgets/common/route_aware.dart';
export 'widgets/common/route_observer.dart';
export 'widgets/deferred_widget.dart';
export 'widgets/disable_widget.dart';
export 'widgets/error_page.dart';
export 'widgets/keep_alive_widget.dart';
export 'widgets/lifecycle_event_handler.dart';
export 'widgets/limit_text_scale_widget.dart';
export 'widgets/material_tip.dart';
export 'widgets/platform_error/platform_error.dart';
export 'widgets/preload_page_view.dart';
export 'widgets/preview_json.dart';
export 'widgets/radio_button.dart';
export 'widgets/radio_option/radio_option.dart';
export 'widgets/skeleton_widget/skeleton_widget.dart';
export 'widgets/switch_button_text.dart';
export 'widgets/switcher_widget.dart';
export 'widgets/text_showmore.dart';
export 'widgets/timeago/timeago.dart';
export 'widgets/void_widget.dart';
export 'widgets/will_pop_scope.dart';
