<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the SplayTreeMap.fromIterable constructor from the Class SplayTreeMap class from the dart:collection library, for the Dart programming language.">
  <title>SplayTreeMap.fromIterable constructor - SplayTreeMap class - dart:collection library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
    <li><a href="dart-collection/SplayTreeMap-class.html">SplayTreeMap<span class="signature">&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span></a></li>
    <li class="self-crumb">SplayTreeMap.fromIterable factory constructor</li>
  </ol>
  <div class="self-name">SplayTreeMap.fromIterable</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
      <li><a href="dart-collection/SplayTreeMap-class.html">SplayTreeMap<span class="signature">&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span></a></li>
      <li class="self-crumb">SplayTreeMap.fromIterable factory constructor</li>
    </ol>
    
    <h5>SplayTreeMap class</h5>
    <ol>
    
      <li class="section-title"><a href="dart-collection/SplayTreeMap-class.html#constructors">Constructors</a></li>
      <li><a href="dart-collection/SplayTreeMap/SplayTreeMap.html">SplayTreeMap</a></li>
      <li><a href="dart-collection/SplayTreeMap/SplayTreeMap.from.html">from</a></li>
      <li><a href="dart-collection/SplayTreeMap/SplayTreeMap.fromIterable.html">fromIterable</a></li>
      <li><a href="dart-collection/SplayTreeMap/SplayTreeMap.fromIterables.html">fromIterables</a></li>
      <li><a href="dart-collection/SplayTreeMap/SplayTreeMap.of.html">of</a></li>
    
      <li class="section-title">
        <a href="dart-collection/SplayTreeMap-class.html#instance-properties">Properties</a>
      </li>
      <li><a href="dart-collection/SplayTreeMap/entries.html">entries</a></li>
      <li><a href="dart-collection/SplayTreeMap/isEmpty.html">isEmpty</a></li>
      <li><a href="dart-collection/SplayTreeMap/isNotEmpty.html">isNotEmpty</a></li>
      <li><a href="dart-collection/SplayTreeMap/keys.html">keys</a></li>
      <li><a href="dart-collection/SplayTreeMap/length.html">length</a></li>
      <li><a href="dart-collection/SplayTreeMap/values.html">values</a></li>
      <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
      <li class="section-title"><a href="dart-collection/SplayTreeMap-class.html#instance-methods">Methods</a></li>
      <li><a href="dart-collection/SplayTreeMap/addAll.html">addAll</a></li>
      <li><a href="dart-collection/SplayTreeMap/clear.html">clear</a></li>
      <li><a href="dart-collection/SplayTreeMap/containsKey.html">containsKey</a></li>
      <li><a href="dart-collection/SplayTreeMap/containsValue.html">containsValue</a></li>
      <li><a href="dart-collection/SplayTreeMap/firstKey.html">firstKey</a></li>
      <li><a href="dart-collection/SplayTreeMap/firstKeyAfter.html">firstKeyAfter</a></li>
      <li><a href="dart-collection/SplayTreeMap/forEach.html">forEach</a></li>
      <li><a href="dart-collection/SplayTreeMap/lastKey.html">lastKey</a></li>
      <li><a href="dart-collection/SplayTreeMap/lastKeyBefore.html">lastKeyBefore</a></li>
      <li><a href="dart-collection/SplayTreeMap/putIfAbsent.html">putIfAbsent</a></li>
      <li><a href="dart-collection/SplayTreeMap/remove.html">remove</a></li>
      <li><a href="dart-collection/SplayTreeMap/update.html">update</a></li>
      <li><a href="dart-collection/SplayTreeMap/updateAll.html">updateAll</a></li>
      <li class="inherited"><a href="dart-collection/MapMixin/addEntries.html">addEntries</a></li>
      <li class="inherited"><a href="dart-collection/MapMixin/cast.html">cast</a></li>
      <li class="inherited"><a href="dart-collection/MapMixin/map.html">map</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
      <li class="inherited"><a href="dart-collection/MapMixin/removeWhere.html">removeWhere</a></li>
      <li class="inherited"><a href="dart-collection/MapMixin/toString.html">toString</a></li>
    
      <li class="section-title"><a href="dart-collection/SplayTreeMap-class.html#operators">Operators</a></li>
      <li><a href="dart-collection/SplayTreeMap/operator_get.html">operator []</a></li>
      <li><a href="dart-collection/SplayTreeMap/operator_put.html">operator []=</a></li>
      <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-left-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-constructor">SplayTreeMap&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;.fromIterable</span> constructor</h1></div>

    <section class="multi-line-signature">
      
      <span class="name ">SplayTreeMap&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;.fromIterable</span>(<wbr><span class="parameter" id="fromIterable-param-iterable"><span class="type-annotation"><a href="dart-core/Iterable-class.html">Iterable</a></span> <span class="parameter-name">iterable</span>, {</span> <span class="parameter" id="fromIterable-param-key"><span class="type-annotation">K</span> <span class="parameter-name">key</span>(<span class="parameter" id="param-element"><span class="type-annotation">dynamic</span> <span class="parameter-name">element</span></span>), </span> <span class="parameter" id="fromIterable-param-value"><span class="type-annotation">V</span> <span class="parameter-name">value</span>(<span class="parameter" id="param-element"><span class="type-annotation">dynamic</span> <span class="parameter-name">element</span></span>), </span> <span class="parameter" id="fromIterable-param-compare"><span class="type-annotation"><a href="dart-core/int-class.html">int</a></span> <span class="parameter-name">compare</span>(<span class="parameter" id="param-key1"><span class="type-annotation">K</span> <span class="parameter-name">key1</span></span> <span class="parameter" id="param-key2"><span class="type-annotation">K</span> <span class="parameter-name">key2</span></span>), </span> <span class="parameter" id="fromIterable-param-isValidKey"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">isValidKey</span>(<span class="parameter" id="param-potentialKey"><span class="type-annotation">dynamic</span> <span class="parameter-name">potentialKey</span></span>)</span> })
    </section>

    <section class="desc markdown">
      <p>Creates a <a href="dart-collection/SplayTreeMap-class.html">SplayTreeMap</a> where the keys and values are computed from the
<code>iterable</code>.</p>
<p>For each element of the <code>iterable</code> this constructor computes a key/value
pair, by applying <code>key</code> and <code>value</code> respectively.</p>
<p>The keys of the key/value pairs do not need to be unique. The last
occurrence of a key will simply overwrite any previous value.</p>
<p>If no functions are specified for <code>key</code> and <code>value</code> the default is to
use the iterable value itself.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">factory SplayTreeMap.fromIterable(Iterable iterable,
    {K Function(dynamic element)? key,
    V Function(dynamic element)? value,
    int Function(K key1, K key2)? compare,
    bool Function(dynamic potentialKey)? isValidKey}) {
  SplayTreeMap&lt;K, V&gt; map = SplayTreeMap&lt;K, V&gt;(compare, isValidKey);
  MapBase._fillMapWithMappedIterable(map, iterable, key, value);
  return map;
}</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
