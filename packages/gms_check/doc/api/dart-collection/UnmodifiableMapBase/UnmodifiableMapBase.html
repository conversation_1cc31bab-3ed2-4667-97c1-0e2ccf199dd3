<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the UnmodifiableMapBase constructor from the Class UnmodifiableMapBase class from the dart:collection library, for the Dart programming language.">
  <title>UnmodifiableMapBase constructor - UnmodifiableMapBase class - dart:collection library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
    <li><a href="dart-collection/UnmodifiableMapBase-class.html">UnmodifiableMapBase<span class="signature">&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span></a></li>
    <li class="self-crumb">UnmodifiableMapBase constructor</li>
  </ol>
  <div class="self-name">UnmodifiableMapBase</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
      <li><a href="dart-collection/UnmodifiableMapBase-class.html">UnmodifiableMapBase<span class="signature">&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span></a></li>
      <li class="self-crumb">UnmodifiableMapBase constructor</li>
    </ol>
    
    <h5>UnmodifiableMapBase class</h5>
    <ol>
    
      <li class="section-title"><a href="dart-collection/UnmodifiableMapBase-class.html#constructors">Constructors</a></li>
      <li><a href="dart-collection/UnmodifiableMapBase/UnmodifiableMapBase.html">UnmodifiableMapBase</a></li>
    
      <li class="section-title inherited">
        <a href="dart-collection/UnmodifiableMapBase-class.html#instance-properties">Properties</a>
      </li>
      <li class="inherited"><a href="dart-collection/MapMixin/entries.html">entries</a></li>
      <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
      <li class="inherited"><a href="dart-collection/MapMixin/isEmpty.html">isEmpty</a></li>
      <li class="inherited"><a href="dart-collection/MapMixin/isNotEmpty.html">isNotEmpty</a></li>
      <li class="inherited"><a href="dart-core/Map/keys.html">keys</a></li>
      <li class="inherited"><a href="dart-collection/MapMixin/length.html">length</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
      <li class="inherited"><a href="dart-collection/MapMixin/values.html">values</a></li>
    
      <li class="section-title inherited"><a href="dart-collection/UnmodifiableMapBase-class.html#instance-methods">Methods</a></li>
      <li class="inherited"><a href="dart-collection/UnmodifiableMapBase/addAll.html">addAll</a></li>
      <li class="inherited"><a href="dart-collection/UnmodifiableMapBase/addEntries.html">addEntries</a></li>
      <li class="inherited"><a href="dart-collection/MapMixin/cast.html">cast</a></li>
      <li class="inherited"><a href="dart-collection/UnmodifiableMapBase/clear.html">clear</a></li>
      <li class="inherited"><a href="dart-collection/MapMixin/containsKey.html">containsKey</a></li>
      <li class="inherited"><a href="dart-collection/MapMixin/containsValue.html">containsValue</a></li>
      <li class="inherited"><a href="dart-collection/MapMixin/forEach.html">forEach</a></li>
      <li class="inherited"><a href="dart-collection/MapMixin/map.html">map</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
      <li class="inherited"><a href="dart-collection/UnmodifiableMapBase/putIfAbsent.html">putIfAbsent</a></li>
      <li class="inherited"><a href="dart-collection/UnmodifiableMapBase/remove.html">remove</a></li>
      <li class="inherited"><a href="dart-collection/UnmodifiableMapBase/removeWhere.html">removeWhere</a></li>
      <li class="inherited"><a href="dart-collection/MapMixin/toString.html">toString</a></li>
      <li class="inherited"><a href="dart-collection/UnmodifiableMapBase/update.html">update</a></li>
      <li class="inherited"><a href="dart-collection/UnmodifiableMapBase/updateAll.html">updateAll</a></li>
    
      <li class="section-title inherited"><a href="dart-collection/UnmodifiableMapBase-class.html#operators">Operators</a></li>
      <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
      <li class="inherited"><a href="dart-core/Map/operator_get.html">operator []</a></li>
      <li class="inherited"><a href="dart-collection/UnmodifiableMapBase/operator_put.html">operator []=</a></li>
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-left-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-constructor">UnmodifiableMapBase&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span> constructor</h1></div>

    <section class="multi-line-signature">
      
      <span class="name ">UnmodifiableMapBase&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span>(<wbr>)
    </section>

    
    

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
