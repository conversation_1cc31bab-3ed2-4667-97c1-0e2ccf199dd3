<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="dart:collection library API docs, for the Dart programming language.">
  <title>dart:collection library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li class="self-crumb">dart:collection library</li>
  </ol>
  <div class="self-name">dart:collection</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li class="self-crumb">dart:collection library</li>
    </ol>
    
    <h5><span class="package-name">gms_check</span> <span class="package-kind">package</span></h5>
    <ol>
          <li class="section-title">Libraries</li>
          <li><a href="gms_check/gms_check-library.html">gms_check</a></li>
          <li class="section-title">Dart</li>
          <li><a href="dart-ui/dart-ui-library.html">dart:ui</a></li>
          <li class="section-subtitle">Core</li>
            <li class="section-subitem"><a href="dart-async/dart-async-library.html">dart:async</a></li>
            <li class="section-subitem"><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
            <li class="section-subitem"><a href="dart-convert/dart-convert-library.html">dart:convert</a></li>
            <li class="section-subitem"><a href="dart-core/dart-core-library.html">dart:core</a></li>
            <li class="section-subitem"><a href="dart-developer/dart-developer-library.html">dart:developer</a></li>
            <li class="section-subitem"><a href="dart-math/dart-math-library.html">dart:math</a></li>
            <li class="section-subitem"><a href="dart-typed_data/dart-typed_data-library.html">dart:typed_data</a></li>
          <li class="section-subtitle">VM</li>
            <li class="section-subitem"><a href="dart-ffi/dart-ffi-library.html">dart:ffi</a></li>
            <li class="section-subitem"><a href="dart-io/dart-io-library.html">dart:io</a></li>
            <li class="section-subitem"><a href="dart-isolate/dart-isolate-library.html">dart:isolate</a></li>
          <li class="section-subtitle">Web</li>
            <li class="section-subitem"><a href="dart-html/dart-html-library.html">dart:html</a></li>
            <li class="section-subitem"><a href="dart-js/dart-js-library.html">dart:js</a></li>
            <li class="section-subitem"><a href="dart-js_util/dart-js_util-library.html">dart:js_util</a></li>
    </ol>
  </div>

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-library">dart:collection</span> library </h1></div>

    <section class="desc markdown">
      <p>Classes and utilities that supplement the collection support in dart:core.</p>
<p>To use this library in your code:</p>
<pre class="language-dart"><code class="language-dart">import 'dart:collection';
</code></pre>
    </section>
    
    <section class="summary offset-anchor" id="classes">
      <h2>Classes</h2>

      <dl>
        <dt id="DoubleLinkedQueue">
          <span class="name "><a href="dart-collection/DoubleLinkedQueue-class.html">DoubleLinkedQueue</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span>         
        </dt>
        <dd>
          A <a href="dart-collection/Queue-class.html">Queue</a> implementation based on a double-linked list. <a href="dart-collection/DoubleLinkedQueue-class.html">[...]</a>
        </dd>
        <dt id="DoubleLinkedQueueEntry">
          <span class="name "><a href="dart-collection/DoubleLinkedQueueEntry-class.html">DoubleLinkedQueueEntry</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span>         
        </dt>
        <dd>
          An entry in a doubly linked list. It contains a pointer to the next
entry, the previous entry, and the boxed element.
        </dd>
        <dt id="HashMap">
          <span class="name "><a href="dart-collection/HashMap-class.html">HashMap</a><span class="signature">&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span></span>         
        </dt>
        <dd>
          A hash-table based implementation of <a href="dart-core/Map-class.html">Map</a>. <a href="dart-collection/HashMap-class.html">[...]</a>
        </dd>
        <dt id="HashSet">
          <span class="name "><a href="dart-collection/HashSet-class.html">HashSet</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span>         
        </dt>
        <dd>
          An unordered hash-table based <a href="dart-core/Set-class.html">Set</a> implementation. <a href="dart-collection/HashSet-class.html">[...]</a>
        </dd>
        <dt id="HasNextIterator">
          <span class="name "><a href="dart-collection/HasNextIterator-class.html">HasNextIterator</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span>         
        </dt>
        <dd>
          The <a href="dart-collection/HasNextIterator-class.html">HasNextIterator</a> class wraps an <a href="dart-core/Iterator-class.html">Iterator</a> and provides methods to
iterate over an object using <code>hasNext</code> and <code>next</code>. <a href="dart-collection/HasNextIterator-class.html">[...]</a>
        </dd>
        <dt id="IterableBase">
          <span class="name "><a href="dart-collection/IterableBase-class.html">IterableBase</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span>         
        </dt>
        <dd>
          Base class for implementing <a href="dart-core/Iterable-class.html">Iterable</a>. <a href="dart-collection/IterableBase-class.html">[...]</a>
        </dd>
        <dt id="IterableMixin">
          <span class="name "><a href="dart-collection/IterableMixin-class.html">IterableMixin</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span>         
        </dt>
        <dd>
          This <a href="dart-core/Iterable-class.html">Iterable</a> mixin implements all <a href="dart-core/Iterable-class.html">Iterable</a> members except <code>iterator</code>. <a href="dart-collection/IterableMixin-class.html">[...]</a>
        </dd>
        <dt id="LinkedHashMap">
          <span class="name "><a href="dart-collection/LinkedHashMap-class.html">LinkedHashMap</a><span class="signature">&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span></span>         
        </dt>
        <dd>
          A hash-table based implementation of <a href="dart-core/Map-class.html">Map</a>. <a href="dart-collection/LinkedHashMap-class.html">[...]</a>
        </dd>
        <dt id="LinkedHashSet">
          <span class="name "><a href="dart-collection/LinkedHashSet-class.html">LinkedHashSet</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span>         
        </dt>
        <dd>
          A <a href="dart-collection/LinkedHashSet-class.html">LinkedHashSet</a> is a hash-table based <a href="dart-core/Set-class.html">Set</a> implementation. <a href="dart-collection/LinkedHashSet-class.html">[...]</a>
        </dd>
        <dt id="LinkedList">
          <span class="name "><a href="dart-collection/LinkedList-class.html">LinkedList</a><span class="signature">&lt;<wbr><span class="type-parameter">E extends <a href="dart-collection/LinkedListEntry-class.html">LinkedListEntry</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span>&gt;</span></span>         
        </dt>
        <dd>
          A specialized double-linked list of elements that extends <a href="dart-collection/LinkedListEntry-class.html">LinkedListEntry</a>. <a href="dart-collection/LinkedList-class.html">[...]</a>
        </dd>
        <dt id="LinkedListEntry">
          <span class="name "><a href="dart-collection/LinkedListEntry-class.html">LinkedListEntry</a><span class="signature">&lt;<wbr><span class="type-parameter">E extends <a href="dart-collection/LinkedListEntry-class.html">LinkedListEntry</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span>&gt;</span></span>         
        </dt>
        <dd>
          An object that can be an element in a <a href="dart-collection/LinkedList-class.html">LinkedList</a>. <a href="dart-collection/LinkedListEntry-class.html">[...]</a>
        </dd>
        <dt id="ListBase">
          <span class="name "><a href="dart-collection/ListBase-class.html">ListBase</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span>         
        </dt>
        <dd>
          Abstract implementation of a list. <a href="dart-collection/ListBase-class.html">[...]</a>
        </dd>
        <dt id="ListMixin">
          <span class="name "><a href="dart-collection/ListMixin-class.html">ListMixin</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span>         
        </dt>
        <dd>
          Base implementation of a <a href="dart-core/List-class.html">List</a> class. <a href="dart-collection/ListMixin-class.html">[...]</a>
        </dd>
        <dt id="ListQueue">
          <span class="name "><a href="dart-collection/ListQueue-class.html">ListQueue</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span>         
        </dt>
        <dd>
          List based <a href="dart-collection/Queue-class.html">Queue</a>. <a href="dart-collection/ListQueue-class.html">[...]</a>
        </dd>
        <dt id="MapBase">
          <span class="name "><a href="dart-collection/MapBase-class.html">MapBase</a><span class="signature">&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span></span>         
        </dt>
        <dd>
          Base class for implementing a <a href="dart-core/Map-class.html">Map</a>. <a href="dart-collection/MapBase-class.html">[...]</a>
        </dd>
        <dt id="MapMixin">
          <span class="name "><a href="dart-collection/MapMixin-class.html">MapMixin</a><span class="signature">&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span></span>         
        </dt>
        <dd>
          Mixin implementing a <a href="dart-core/Map-class.html">Map</a>. <a href="dart-collection/MapMixin-class.html">[...]</a>
        </dd>
        <dt id="MapView">
          <span class="name "><a href="dart-collection/MapView-class.html">MapView</a><span class="signature">&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span></span>         
        </dt>
        <dd>
          Wrapper around a class that implements <a href="dart-core/Map-class.html">Map</a> that only exposes <code>Map</code>
members. <a href="dart-collection/MapView-class.html">[...]</a>
        </dd>
        <dt id="Queue">
          <span class="name "><a href="dart-collection/Queue-class.html">Queue</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span>         
        </dt>
        <dd>
          A <a href="dart-collection/Queue-class.html">Queue</a> is a collection that can be manipulated at both ends. One
can iterate over the elements of a queue through <a href="dart-core/Iterable/forEach.html">forEach</a> or with
an <a href="dart-core/Iterator-class.html">Iterator</a>. <a href="dart-collection/Queue-class.html">[...]</a>
        </dd>
        <dt id="SetBase">
          <span class="name "><a href="dart-collection/SetBase-class.html">SetBase</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span>         
        </dt>
        <dd>
          Base implementation of <a href="dart-core/Set-class.html">Set</a>. <a href="dart-collection/SetBase-class.html">[...]</a>
        </dd>
        <dt id="SetMixin">
          <span class="name "><a href="dart-collection/SetMixin-class.html">SetMixin</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span>         
        </dt>
        <dd>
          Mixin implementation of <a href="dart-core/Set-class.html">Set</a>. <a href="dart-collection/SetMixin-class.html">[...]</a>
        </dd>
        <dt id="SplayTreeMap">
          <span class="name "><a href="dart-collection/SplayTreeMap-class.html">SplayTreeMap</a><span class="signature">&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span></span>         
        </dt>
        <dd>
          A <a href="dart-core/Map-class.html">Map</a> of objects that can be ordered relative to each other. <a href="dart-collection/SplayTreeMap-class.html">[...]</a>
        </dd>
        <dt id="SplayTreeSet">
          <span class="name "><a href="dart-collection/SplayTreeSet-class.html">SplayTreeSet</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span>         
        </dt>
        <dd>
          A <a href="dart-core/Set-class.html">Set</a> of objects that can be ordered relative to each other. <a href="dart-collection/SplayTreeSet-class.html">[...]</a>
        </dd>
        <dt id="UnmodifiableListView">
          <span class="name "><a href="dart-collection/UnmodifiableListView-class.html">UnmodifiableListView</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span>         
        </dt>
        <dd>
          An unmodifiable <a href="dart-core/List-class.html">List</a> view of another List. <a href="dart-collection/UnmodifiableListView-class.html">[...]</a>
        </dd>
        <dt id="UnmodifiableMapBase">
          <span class="name "><a href="dart-collection/UnmodifiableMapBase-class.html">UnmodifiableMapBase</a><span class="signature">&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span></span>         
        </dt>
        <dd>
          Basic implementation of an unmodifiable <a href="dart-core/Map-class.html">Map</a>. <a href="dart-collection/UnmodifiableMapBase-class.html">[...]</a>
        </dd>
        <dt id="UnmodifiableMapView">
          <span class="name "><a href="dart-collection/UnmodifiableMapView-class.html">UnmodifiableMapView</a><span class="signature">&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span></span>         
        </dt>
        <dd>
          View of a <a href="dart-core/Map-class.html">Map</a> that disallow modifying the map. <a href="dart-collection/UnmodifiableMapView-class.html">[...]</a>
        </dd>
        <dt id="UnmodifiableSetView">
          <span class="name "><a href="dart-collection/UnmodifiableSetView-class.html">UnmodifiableSetView</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span>         
        </dt>
        <dd>
          An unmodifiable <a href="dart-core/Set-class.html">Set</a> view of another <a href="dart-core/Set-class.html">Set</a>. <a href="dart-collection/UnmodifiableSetView-class.html">[...]</a>
        </dd>
      </dl>
    </section>









  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
    <h5>dart:collection library</h5>
    <ol>
      <li class="section-title"><a href="dart-collection/dart-collection-library.html#classes">Classes</a></li>
      <li><a href="dart-collection/DoubleLinkedQueue-class.html">DoubleLinkedQueue</a></li>
      <li><a href="dart-collection/DoubleLinkedQueueEntry-class.html">DoubleLinkedQueueEntry</a></li>
      <li><a href="dart-collection/HashMap-class.html">HashMap</a></li>
      <li><a href="dart-collection/HashSet-class.html">HashSet</a></li>
      <li><a href="dart-collection/HasNextIterator-class.html">HasNextIterator</a></li>
      <li><a href="dart-collection/IterableBase-class.html">IterableBase</a></li>
      <li><a href="dart-collection/IterableMixin-class.html">IterableMixin</a></li>
      <li><a href="dart-collection/LinkedHashMap-class.html">LinkedHashMap</a></li>
      <li><a href="dart-collection/LinkedHashSet-class.html">LinkedHashSet</a></li>
      <li><a href="dart-collection/LinkedList-class.html">LinkedList</a></li>
      <li><a href="dart-collection/LinkedListEntry-class.html">LinkedListEntry</a></li>
      <li><a href="dart-collection/ListBase-class.html">ListBase</a></li>
      <li><a href="dart-collection/ListMixin-class.html">ListMixin</a></li>
      <li><a href="dart-collection/ListQueue-class.html">ListQueue</a></li>
      <li><a href="dart-collection/MapBase-class.html">MapBase</a></li>
      <li><a href="dart-collection/MapMixin-class.html">MapMixin</a></li>
      <li><a href="dart-collection/MapView-class.html">MapView</a></li>
      <li><a href="dart-collection/Queue-class.html">Queue</a></li>
      <li><a href="dart-collection/SetBase-class.html">SetBase</a></li>
      <li><a href="dart-collection/SetMixin-class.html">SetMixin</a></li>
      <li><a href="dart-collection/SplayTreeMap-class.html">SplayTreeMap</a></li>
      <li><a href="dart-collection/SplayTreeSet-class.html">SplayTreeSet</a></li>
      <li><a href="dart-collection/UnmodifiableListView-class.html">UnmodifiableListView</a></li>
      <li><a href="dart-collection/UnmodifiableMapBase-class.html">UnmodifiableMapBase</a></li>
      <li><a href="dart-collection/UnmodifiableMapView-class.html">UnmodifiableMapView</a></li>
      <li><a href="dart-collection/UnmodifiableSetView-class.html">UnmodifiableSetView</a></li>
    
    
    
    
    
    
    
    
    </ol>
  </div><!--/sidebar-offcanvas-right-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
