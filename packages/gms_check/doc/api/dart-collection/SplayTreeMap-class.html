<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the SplayTreeMap class from the dart:collection library, for the Dart programming language.">
  <title>SplayTreeMap class - dart:collection library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
    <li class="self-crumb">SplayTreeMap<span class="signature">&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span> class</li>
  </ol>
  <div class="self-name">SplayTreeMap</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
      <li class="self-crumb">SplayTreeMap<span class="signature">&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span> class</li>
    </ol>
    
    <h5>dart:collection library</h5>
    <ol>
      <li class="section-title"><a href="dart-collection/dart-collection-library.html#classes">Classes</a></li>
      <li><a href="dart-collection/DoubleLinkedQueue-class.html">DoubleLinkedQueue</a></li>
      <li><a href="dart-collection/DoubleLinkedQueueEntry-class.html">DoubleLinkedQueueEntry</a></li>
      <li><a href="dart-collection/HashMap-class.html">HashMap</a></li>
      <li><a href="dart-collection/HashSet-class.html">HashSet</a></li>
      <li><a href="dart-collection/HasNextIterator-class.html">HasNextIterator</a></li>
      <li><a href="dart-collection/IterableBase-class.html">IterableBase</a></li>
      <li><a href="dart-collection/IterableMixin-class.html">IterableMixin</a></li>
      <li><a href="dart-collection/LinkedHashMap-class.html">LinkedHashMap</a></li>
      <li><a href="dart-collection/LinkedHashSet-class.html">LinkedHashSet</a></li>
      <li><a href="dart-collection/LinkedList-class.html">LinkedList</a></li>
      <li><a href="dart-collection/LinkedListEntry-class.html">LinkedListEntry</a></li>
      <li><a href="dart-collection/ListBase-class.html">ListBase</a></li>
      <li><a href="dart-collection/ListMixin-class.html">ListMixin</a></li>
      <li><a href="dart-collection/ListQueue-class.html">ListQueue</a></li>
      <li><a href="dart-collection/MapBase-class.html">MapBase</a></li>
      <li><a href="dart-collection/MapMixin-class.html">MapMixin</a></li>
      <li><a href="dart-collection/MapView-class.html">MapView</a></li>
      <li><a href="dart-collection/Queue-class.html">Queue</a></li>
      <li><a href="dart-collection/SetBase-class.html">SetBase</a></li>
      <li><a href="dart-collection/SetMixin-class.html">SetMixin</a></li>
      <li><a href="dart-collection/SplayTreeMap-class.html">SplayTreeMap</a></li>
      <li><a href="dart-collection/SplayTreeSet-class.html">SplayTreeSet</a></li>
      <li><a href="dart-collection/UnmodifiableListView-class.html">UnmodifiableListView</a></li>
      <li><a href="dart-collection/UnmodifiableMapBase-class.html">UnmodifiableMapBase</a></li>
      <li><a href="dart-collection/UnmodifiableMapView-class.html">UnmodifiableMapView</a></li>
      <li><a href="dart-collection/UnmodifiableSetView-class.html">UnmodifiableSetView</a></li>
    
    
    
    
    
    
    
    
    </ol>
  </div>

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-class">SplayTreeMap&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span> class </h1></div>

    <section class="desc markdown">
      <p>A <a href="dart-core/Map-class.html">Map</a> of objects that can be ordered relative to each other.</p>
<p>The map is based on a self-balancing binary tree.
It allows most single-entry operations in amortized logarithmic time.</p>
<p>Keys of the map are compared using the <code>compare</code> function passed in
the constructor, both for ordering and for equality.
If the map contains only the key <code>a</code>, then <code>map.containsKey(b)</code>
will return <code>true</code> if and only if <code>compare(a, b) == 0</code>,
and the value of <code>a == b</code> is not even checked.
If the compare function is omitted, the objects are assumed to be
<a href="dart-core/Comparable-class.html">Comparable</a>, and are compared using their <a href="dart-core/Comparable/compareTo.html">Comparable.compareTo</a> method.
Non-comparable objects (including <code>null</code>) will not work as keys
in that case.</p>
<p>To allow calling <a href="dart-collection/SplayTreeMap/operator_get.html">operator []</a>, <a href="dart-collection/SplayTreeMap/remove.html">remove</a> or <a href="dart-collection/SplayTreeMap/containsKey.html">containsKey</a> with objects
that are not supported by the <code>compare</code> function, an extra <code>isValidKey</code>
predicate function can be supplied. This function is tested before
using the <code>compare</code> function on an argument value that may not be a <code>K</code>
value. If omitted, the <code>isValidKey</code> function defaults to testing if the
value is a <code>K</code>.</p>
    </section>
    
    <section>
      <dl class="dl-horizontal">


        <dt>Mixed in types</dt>
        <dd><ul class="comma-separated clazz-relationships">
          <li><a href="dart-collection/MapMixin-class.html">MapMixin</a><span class="signature">&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span></li>
        </ul></dd>



      </dl>
    </section>

    <section class="summary offset-anchor" id="constructors">
      <h2>Constructors</h2>

      <dl class="constructor-summary-list">
        <dt id="SplayTreeMap" class="callable">
          <span class="name"><a href="dart-collection/SplayTreeMap/SplayTreeMap.html">SplayTreeMap</a></span><span class="signature">([<span class="parameter" id="-param-compare"><span class="type-annotation"><a href="dart-core/int-class.html">int</a></span> <span class="parameter-name">compare</span>(<span class="parameter" id="param-key1"><span class="type-annotation">K</span> <span class="parameter-name">key1</span></span> <span class="parameter" id="param-key2"><span class="type-annotation">K</span> <span class="parameter-name">key2</span></span>), </span> <span class="parameter" id="-param-isValidKey"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">isValidKey</span>(<span class="parameter" id="param-potentialKey"><span class="type-annotation">dynamic</span> <span class="parameter-name">potentialKey</span></span>)</span> ])</span>
        </dt>
        <dd>
          
        </dd>
        <dt id="SplayTreeMap.from" class="callable">
          <span class="name"><a href="dart-collection/SplayTreeMap/SplayTreeMap.from.html">SplayTreeMap.from</a></span><span class="signature">(<span class="parameter" id="from-param-other"><span class="type-annotation"><a href="dart-core/Map-class.html">Map</a></span> <span class="parameter-name">other</span>, [</span> <span class="parameter" id="from-param-compare"><span class="type-annotation"><a href="dart-core/int-class.html">int</a></span> <span class="parameter-name">compare</span>(<span class="parameter" id="param-key1"><span class="type-annotation">K</span> <span class="parameter-name">key1</span></span> <span class="parameter" id="param-key2"><span class="type-annotation">K</span> <span class="parameter-name">key2</span></span>), </span> <span class="parameter" id="from-param-isValidKey"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">isValidKey</span>(<span class="parameter" id="param-potentialKey"><span class="type-annotation">dynamic</span> <span class="parameter-name">potentialKey</span></span>)</span> ])</span>
        </dt>
        <dd>
          Creates a <a href="dart-collection/SplayTreeMap-class.html">SplayTreeMap</a> that contains all key/value pairs of <code>other</code>. <a href="dart-collection/SplayTreeMap/SplayTreeMap.from.html">[...]</a>
          <div class="constructor-modifier features">factory</div>
        </dd>
        <dt id="SplayTreeMap.fromIterable" class="callable">
          <span class="name"><a href="dart-collection/SplayTreeMap/SplayTreeMap.fromIterable.html">SplayTreeMap.fromIterable</a></span><span class="signature">(<span class="parameter" id="fromIterable-param-iterable"><span class="type-annotation"><a href="dart-core/Iterable-class.html">Iterable</a></span> <span class="parameter-name">iterable</span>, {</span> <span class="parameter" id="fromIterable-param-key"><span class="type-annotation">K</span> <span class="parameter-name">key</span>(<span class="parameter" id="param-element"><span class="type-annotation">dynamic</span> <span class="parameter-name">element</span></span>), </span> <span class="parameter" id="fromIterable-param-value"><span class="type-annotation">V</span> <span class="parameter-name">value</span>(<span class="parameter" id="param-element"><span class="type-annotation">dynamic</span> <span class="parameter-name">element</span></span>), </span> <span class="parameter" id="fromIterable-param-compare"><span class="type-annotation"><a href="dart-core/int-class.html">int</a></span> <span class="parameter-name">compare</span>(<span class="parameter" id="param-key1"><span class="type-annotation">K</span> <span class="parameter-name">key1</span></span> <span class="parameter" id="param-key2"><span class="type-annotation">K</span> <span class="parameter-name">key2</span></span>), </span> <span class="parameter" id="fromIterable-param-isValidKey"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">isValidKey</span>(<span class="parameter" id="param-potentialKey"><span class="type-annotation">dynamic</span> <span class="parameter-name">potentialKey</span></span>)</span> })</span>
        </dt>
        <dd>
          Creates a <a href="dart-collection/SplayTreeMap-class.html">SplayTreeMap</a> where the keys and values are computed from the
<code>iterable</code>. <a href="dart-collection/SplayTreeMap/SplayTreeMap.fromIterable.html">[...]</a>
          <div class="constructor-modifier features">factory</div>
        </dd>
        <dt id="SplayTreeMap.fromIterables" class="callable">
          <span class="name"><a href="dart-collection/SplayTreeMap/SplayTreeMap.fromIterables.html">SplayTreeMap.fromIterables</a></span><span class="signature">(<span class="parameter" id="fromIterables-param-keys"><span class="type-annotation"><a href="dart-core/Iterable-class.html">Iterable</a><span class="signature">&lt;<wbr><span class="type-parameter">K</span>&gt;</span></span> <span class="parameter-name">keys</span>, </span> <span class="parameter" id="fromIterables-param-values"><span class="type-annotation"><a href="dart-core/Iterable-class.html">Iterable</a><span class="signature">&lt;<wbr><span class="type-parameter">V</span>&gt;</span></span> <span class="parameter-name">values</span>, [</span> <span class="parameter" id="fromIterables-param-compare"><span class="type-annotation"><a href="dart-core/int-class.html">int</a></span> <span class="parameter-name">compare</span>(<span class="parameter" id="param-key1"><span class="type-annotation">K</span> <span class="parameter-name">key1</span></span> <span class="parameter" id="param-key2"><span class="type-annotation">K</span> <span class="parameter-name">key2</span></span>), </span> <span class="parameter" id="fromIterables-param-isValidKey"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">isValidKey</span>(<span class="parameter" id="param-potentialKey"><span class="type-annotation">dynamic</span> <span class="parameter-name">potentialKey</span></span>)</span> ])</span>
        </dt>
        <dd>
          Creates a <a href="dart-collection/SplayTreeMap-class.html">SplayTreeMap</a> associating the given <code>keys</code> to <code>values</code>. <a href="dart-collection/SplayTreeMap/SplayTreeMap.fromIterables.html">[...]</a>
          <div class="constructor-modifier features">factory</div>
        </dd>
        <dt id="SplayTreeMap.of" class="callable">
          <span class="name"><a href="dart-collection/SplayTreeMap/SplayTreeMap.of.html">SplayTreeMap.of</a></span><span class="signature">(<span class="parameter" id="of-param-other"><span class="type-annotation"><a href="dart-core/Map-class.html">Map</a><span class="signature">&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span></span> <span class="parameter-name">other</span>, [</span> <span class="parameter" id="of-param-compare"><span class="type-annotation"><a href="dart-core/int-class.html">int</a></span> <span class="parameter-name">compare</span>(<span class="parameter" id="param-key1"><span class="type-annotation">K</span> <span class="parameter-name">key1</span></span> <span class="parameter" id="param-key2"><span class="type-annotation">K</span> <span class="parameter-name">key2</span></span>), </span> <span class="parameter" id="of-param-isValidKey"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">isValidKey</span>(<span class="parameter" id="param-potentialKey"><span class="type-annotation">dynamic</span> <span class="parameter-name">potentialKey</span></span>)</span> ])</span>
        </dt>
        <dd>
          Creates a <a href="dart-collection/SplayTreeMap-class.html">SplayTreeMap</a> that contains all key/value pairs of <code>other</code>.
          <div class="constructor-modifier features">factory</div>
        </dd>
      </dl>
    </section>

    <section class="summary offset-anchor" id="instance-properties">
      <h2>Properties</h2>

      <dl class="properties">
        <dt id="entries" class="property">
          <span class="name"><a href="dart-collection/SplayTreeMap/entries.html">entries</a></span>
          <span class="signature">&#8594; <a href="dart-core/Iterable-class.html">Iterable</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-core/MapEntry-class.html">MapEntry</a><span class="signature">&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span></span>&gt;</span></span>         
        </dt>
        <dd>
          The map entries of <a href="dart-collection/SplayTreeMap-class.html">this</a>.
                  <div class="features">read-only, override</div>
</dd>
        <dt id="isEmpty" class="property">
          <span class="name"><a href="dart-collection/SplayTreeMap/isEmpty.html">isEmpty</a></span>
          <span class="signature">&#8594; <a href="dart-core/bool-class.html">bool</a></span>         
        </dt>
        <dd>
          Whether there is no key/value pair in the map.
                  <div class="features">read-only, override</div>
</dd>
        <dt id="isNotEmpty" class="property">
          <span class="name"><a href="dart-collection/SplayTreeMap/isNotEmpty.html">isNotEmpty</a></span>
          <span class="signature">&#8594; <a href="dart-core/bool-class.html">bool</a></span>         
        </dt>
        <dd>
          Whether there is at least one key/value pair in the map.
                  <div class="features">read-only, override</div>
</dd>
        <dt id="keys" class="property">
          <span class="name"><a href="dart-collection/SplayTreeMap/keys.html">keys</a></span>
          <span class="signature">&#8594; <a href="dart-core/Iterable-class.html">Iterable</a><span class="signature">&lt;<wbr><span class="type-parameter">K</span>&gt;</span></span>         
        </dt>
        <dd>
          The keys of <a href="dart-collection/SplayTreeMap-class.html">this</a>. <a href="dart-collection/SplayTreeMap/keys.html">[...]</a>
                  <div class="features">read-only, override</div>
</dd>
        <dt id="length" class="property">
          <span class="name"><a href="dart-collection/SplayTreeMap/length.html">length</a></span>
          <span class="signature">&#8594; <a href="dart-core/int-class.html">int</a></span>         
        </dt>
        <dd>
          The number of key/value pairs in the map.
                  <div class="features">read-only, override</div>
</dd>
        <dt id="values" class="property">
          <span class="name"><a href="dart-collection/SplayTreeMap/values.html">values</a></span>
          <span class="signature">&#8594; <a href="dart-core/Iterable-class.html">Iterable</a><span class="signature">&lt;<wbr><span class="type-parameter">V</span>&gt;</span></span>         
        </dt>
        <dd>
          The values of <a href="dart-collection/SplayTreeMap-class.html">this</a>. <a href="dart-collection/SplayTreeMap/values.html">[...]</a>
                  <div class="features">read-only, override</div>
</dd>
        <dt id="hashCode" class="property inherited">
          <span class="name"><a href="dart-core/Object/hashCode.html">hashCode</a></span>
          <span class="signature">&#8594; <a href="dart-core/int-class.html">int</a></span>         
        </dt>
        <dd class="inherited">
          The hash code for this object. <a href="dart-core/Object/hashCode.html">[...]</a>
                  <div class="features">read-only, inherited</div>
</dd>
        <dt id="runtimeType" class="property inherited">
          <span class="name"><a href="dart-core/Object/runtimeType.html">runtimeType</a></span>
          <span class="signature">&#8594; <a href="dart-core/Type-class.html">Type</a></span>         
        </dt>
        <dd class="inherited">
          A representation of the runtime type of the object.
                  <div class="features">read-only, inherited</div>
</dd>
      </dl>
    </section>

    <section class="summary offset-anchor" id="instance-methods">
      <h2>Methods</h2>
      <dl class="callables">
        <dt id="addAll" class="callable">
          <span class="name"><a href="dart-collection/SplayTreeMap/addAll.html">addAll</a></span><span class="signature">(<wbr><span class="parameter" id="addAll-param-other"><span class="type-annotation"><a href="dart-core/Map-class.html">Map</a><span class="signature">&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span></span> <span class="parameter-name">other</span></span>)
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd>
          Adds all key/value pairs of <code>other</code> to this map. <a href="dart-collection/SplayTreeMap/addAll.html">[...]</a>
                  <div class="features">override</div>
</dd>
        <dt id="clear" class="callable">
          <span class="name"><a href="dart-collection/SplayTreeMap/clear.html">clear</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd>
          Removes all entries from the map. <a href="dart-collection/SplayTreeMap/clear.html">[...]</a>
                  <div class="features">override</div>
</dd>
        <dt id="containsKey" class="callable">
          <span class="name"><a href="dart-collection/SplayTreeMap/containsKey.html">containsKey</a></span><span class="signature">(<wbr><span class="parameter" id="containsKey-param-key"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">key</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/bool-class.html">bool</a></span>
          </span>
                  </dt>
        <dd>
          Whether this map contains the given <code>key</code>. <a href="dart-collection/SplayTreeMap/containsKey.html">[...]</a>
                  <div class="features">override</div>
</dd>
        <dt id="containsValue" class="callable">
          <span class="name"><a href="dart-collection/SplayTreeMap/containsValue.html">containsValue</a></span><span class="signature">(<wbr><span class="parameter" id="containsValue-param-value"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">value</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/bool-class.html">bool</a></span>
          </span>
                  </dt>
        <dd>
          Whether this map contains the given <code>value</code>. <a href="dart-collection/SplayTreeMap/containsValue.html">[...]</a>
                  <div class="features">override</div>
</dd>
        <dt id="firstKey" class="callable">
          <span class="name"><a href="dart-collection/SplayTreeMap/firstKey.html">firstKey</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; K</span>
          </span>
                  </dt>
        <dd>
          The first key in the map. <a href="dart-collection/SplayTreeMap/firstKey.html">[...]</a>
                  
</dd>
        <dt id="firstKeyAfter" class="callable">
          <span class="name"><a href="dart-collection/SplayTreeMap/firstKeyAfter.html">firstKeyAfter</a></span><span class="signature">(<wbr><span class="parameter" id="firstKeyAfter-param-key"><span class="type-annotation">K</span> <span class="parameter-name">key</span></span>)
            <span class="returntype parameter">&#8594; K</span>
          </span>
                  </dt>
        <dd>
          Get the first key in the map that is strictly larger than <code>key</code>. Returns
<code>null</code> if no key was not found.
                  
</dd>
        <dt id="forEach" class="callable">
          <span class="name"><a href="dart-collection/SplayTreeMap/forEach.html">forEach</a></span><span class="signature">(<wbr><span class="parameter" id="forEach-param-f"><span class="type-annotation">void</span> <span class="parameter-name">f</span>(<span class="parameter" id="param-key"><span class="type-annotation">K</span> <span class="parameter-name">key</span>, </span> <span class="parameter" id="param-value"><span class="type-annotation">V</span> <span class="parameter-name">value</span></span>)</span>)
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd>
          Applies <code>action</code> to each key/value pair of the map. <a href="dart-collection/SplayTreeMap/forEach.html">[...]</a>
                  <div class="features">override</div>
</dd>
        <dt id="lastKey" class="callable">
          <span class="name"><a href="dart-collection/SplayTreeMap/lastKey.html">lastKey</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; K</span>
          </span>
                  </dt>
        <dd>
          The last key in the map. <a href="dart-collection/SplayTreeMap/lastKey.html">[...]</a>
                  
</dd>
        <dt id="lastKeyBefore" class="callable">
          <span class="name"><a href="dart-collection/SplayTreeMap/lastKeyBefore.html">lastKeyBefore</a></span><span class="signature">(<wbr><span class="parameter" id="lastKeyBefore-param-key"><span class="type-annotation">K</span> <span class="parameter-name">key</span></span>)
            <span class="returntype parameter">&#8594; K</span>
          </span>
                  </dt>
        <dd>
          The last key in the map that is strictly smaller than <code>key</code>. <a href="dart-collection/SplayTreeMap/lastKeyBefore.html">[...]</a>
                  
</dd>
        <dt id="putIfAbsent" class="callable">
          <span class="name"><a href="dart-collection/SplayTreeMap/putIfAbsent.html">putIfAbsent</a></span><span class="signature">(<wbr><span class="parameter" id="putIfAbsent-param-key"><span class="type-annotation">K</span> <span class="parameter-name">key</span>, </span> <span class="parameter" id="putIfAbsent-param-ifAbsent"><span class="type-annotation">V</span> <span class="parameter-name">ifAbsent</span>()</span>)
            <span class="returntype parameter">&#8594; V</span>
          </span>
                  </dt>
        <dd>
          Look up the value of <code>key</code>, or add a new entry if it isn't there. <a href="dart-collection/SplayTreeMap/putIfAbsent.html">[...]</a>
                  <div class="features">override</div>
</dd>
        <dt id="remove" class="callable">
          <span class="name"><a href="dart-collection/SplayTreeMap/remove.html">remove</a></span><span class="signature">(<wbr><span class="parameter" id="remove-param-key"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">key</span></span>)
            <span class="returntype parameter">&#8594; V</span>
          </span>
                  </dt>
        <dd>
          Removes <code>key</code> and its associated value, if present, from the map. <a href="dart-collection/SplayTreeMap/remove.html">[...]</a>
                  <div class="features">override</div>
</dd>
        <dt id="update" class="callable">
          <span class="name"><a href="dart-collection/SplayTreeMap/update.html">update</a></span><span class="signature">(<wbr><span class="parameter" id="update-param-key"><span class="type-annotation">K</span> <span class="parameter-name">key</span>, </span> <span class="parameter" id="update-param-update"><span class="type-annotation">V</span> <span class="parameter-name">update</span>(<span class="parameter" id="param-value"><span class="type-annotation">V</span> <span class="parameter-name">value</span></span>), {</span> <span class="parameter" id="update-param-ifAbsent"><span class="type-annotation">V</span> <span class="parameter-name">ifAbsent</span>()</span> })
            <span class="returntype parameter">&#8594; V</span>
          </span>
                  </dt>
        <dd>
          Updates the value for the provided <code>key</code>. <a href="dart-collection/SplayTreeMap/update.html">[...]</a>
                  <div class="features">override</div>
</dd>
        <dt id="updateAll" class="callable">
          <span class="name"><a href="dart-collection/SplayTreeMap/updateAll.html">updateAll</a></span><span class="signature">(<wbr><span class="parameter" id="updateAll-param-update"><span class="type-annotation">V</span> <span class="parameter-name">update</span>(<span class="parameter" id="param-key"><span class="type-annotation">K</span> <span class="parameter-name">key</span>, </span> <span class="parameter" id="param-value"><span class="type-annotation">V</span> <span class="parameter-name">value</span></span>)</span>)
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd>
          Updates all values. <a href="dart-collection/SplayTreeMap/updateAll.html">[...]</a>
                  <div class="features">override</div>
</dd>
        <dt id="addEntries" class="callable inherited">
          <span class="name"><a href="dart-collection/MapMixin/addEntries.html">addEntries</a></span><span class="signature">(<wbr><span class="parameter" id="addEntries-param-newEntries"><span class="type-annotation"><a href="dart-core/Iterable-class.html">Iterable</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-core/MapEntry-class.html">MapEntry</a><span class="signature">&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span></span>&gt;</span></span> <span class="parameter-name">newEntries</span></span>)
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd class="inherited">
          Adds all key/value pairs of <code>newEntries</code> to this map. <a href="dart-collection/MapMixin/addEntries.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="cast" class="callable inherited">
          <span class="name"><a href="dart-collection/MapMixin/cast.html">cast</a></span><span class="signature">&lt;<wbr><span class="type-parameter">RK</span>, <span class="type-parameter">RV</span>&gt;</span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-core/Map-class.html">Map</a><span class="signature">&lt;<wbr><span class="type-parameter">RK</span>, <span class="type-parameter">RV</span>&gt;</span></span>
          </span>
                  </dt>
        <dd class="inherited">
          Provides a view of this map as having <code>RK</code> keys and <code>RV</code> instances,
if necessary. <a href="dart-collection/MapMixin/cast.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="map" class="callable inherited">
          <span class="name"><a href="dart-collection/MapMixin/map.html">map</a></span><span class="signature">&lt;<wbr><span class="type-parameter">K2</span>, <span class="type-parameter">V2</span>&gt;</span><span class="signature">(<wbr><span class="parameter" id="map-param-transform"><span class="type-annotation"><a href="dart-core/MapEntry-class.html">MapEntry</a><span class="signature">&lt;<wbr><span class="type-parameter">K2</span>, <span class="type-parameter">V2</span>&gt;</span></span> <span class="parameter-name">transform</span>(<span class="parameter" id="param-key"><span class="type-annotation">K</span> <span class="parameter-name">key</span>, </span> <span class="parameter" id="param-value"><span class="type-annotation">V</span> <span class="parameter-name">value</span></span>)</span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/Map-class.html">Map</a><span class="signature">&lt;<wbr><span class="type-parameter">K2</span>, <span class="type-parameter">V2</span>&gt;</span></span>
          </span>
                  </dt>
        <dd class="inherited">
          Returns a new map where all entries of this map are transformed by
the given <code>convert</code> function.
                  <div class="features">inherited</div>
</dd>
        <dt id="noSuchMethod" class="callable inherited">
          <span class="name"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></span><span class="signature">(<wbr><span class="parameter" id="noSuchMethod-param-invocation"><span class="type-annotation"><a href="dart-core/Invocation-class.html">Invocation</a></span> <span class="parameter-name">invocation</span></span>)
            <span class="returntype parameter">&#8594; dynamic</span>
          </span>
                  </dt>
        <dd class="inherited">
          Invoked when a non-existent method or property is accessed. <a href="dart-core/Object/noSuchMethod.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="removeWhere" class="callable inherited">
          <span class="name"><a href="dart-collection/MapMixin/removeWhere.html">removeWhere</a></span><span class="signature">(<wbr><span class="parameter" id="removeWhere-param-test"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">test</span>(<span class="parameter" id="param-key"><span class="type-annotation">K</span> <span class="parameter-name">key</span>, </span> <span class="parameter" id="param-value"><span class="type-annotation">V</span> <span class="parameter-name">value</span></span>)</span>)
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd class="inherited">
          Removes all entries of this map that satisfy the given <code>test</code>.
                  <div class="features">inherited</div>
</dd>
        <dt id="toString" class="callable inherited">
          <span class="name"><a href="dart-collection/MapMixin/toString.html">toString</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-core/String-class.html">String</a></span>
          </span>
                  </dt>
        <dd class="inherited">
          A string representation of this object. <a href="dart-collection/MapMixin/toString.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
      </dl>
    </section>

    <section class="summary offset-anchor" id="operators">
      <h2>Operators</h2>
      <dl class="callables">
        <dt id="operator []" class="callable">
          <span class="name"><a href="dart-collection/SplayTreeMap/operator_get.html">operator []</a></span><span class="signature">(<wbr><span class="parameter" id="[]-param-key"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">key</span></span>)
            <span class="returntype parameter">&#8594; V</span>
          </span>
                  </dt>
        <dd>
          The value for the given <code>key</code>, or <code>null</code> if <code>key</code> is not in the map. <a href="dart-collection/SplayTreeMap/operator_get.html">[...]</a>
                  <div class="features">override</div>
</dd>
        <dt id="operator []=" class="callable">
          <span class="name"><a href="dart-collection/SplayTreeMap/operator_put.html">operator []=</a></span><span class="signature">(<wbr><span class="parameter" id="[]=-param-key"><span class="type-annotation">K</span> <span class="parameter-name">key</span>, </span> <span class="parameter" id="[]=-param-value"><span class="type-annotation">V</span> <span class="parameter-name">value</span></span>)
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd>
          Associates the <code>key</code> with the given <code>value</code>. <a href="dart-collection/SplayTreeMap/operator_put.html">[...]</a>
                  <div class="features">override</div>
</dd>
        <dt id="operator ==" class="callable inherited">
          <span class="name"><a href="dart-core/Object/operator_equals.html">operator ==</a></span><span class="signature">(<wbr><span class="parameter" id="==-param-other"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">other</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/bool-class.html">bool</a></span>
          </span>
                  </dt>
        <dd class="inherited">
          The equality operator. <a href="dart-core/Object/operator_equals.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
      </dl>
    </section>




  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
    <ol>
    
      <li class="section-title"><a href="dart-collection/SplayTreeMap-class.html#constructors">Constructors</a></li>
      <li><a href="dart-collection/SplayTreeMap/SplayTreeMap.html">SplayTreeMap</a></li>
      <li><a href="dart-collection/SplayTreeMap/SplayTreeMap.from.html">from</a></li>
      <li><a href="dart-collection/SplayTreeMap/SplayTreeMap.fromIterable.html">fromIterable</a></li>
      <li><a href="dart-collection/SplayTreeMap/SplayTreeMap.fromIterables.html">fromIterables</a></li>
      <li><a href="dart-collection/SplayTreeMap/SplayTreeMap.of.html">of</a></li>
    
      <li class="section-title">
        <a href="dart-collection/SplayTreeMap-class.html#instance-properties">Properties</a>
      </li>
      <li><a href="dart-collection/SplayTreeMap/entries.html">entries</a></li>
      <li><a href="dart-collection/SplayTreeMap/isEmpty.html">isEmpty</a></li>
      <li><a href="dart-collection/SplayTreeMap/isNotEmpty.html">isNotEmpty</a></li>
      <li><a href="dart-collection/SplayTreeMap/keys.html">keys</a></li>
      <li><a href="dart-collection/SplayTreeMap/length.html">length</a></li>
      <li><a href="dart-collection/SplayTreeMap/values.html">values</a></li>
      <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
      <li class="section-title"><a href="dart-collection/SplayTreeMap-class.html#instance-methods">Methods</a></li>
      <li><a href="dart-collection/SplayTreeMap/addAll.html">addAll</a></li>
      <li><a href="dart-collection/SplayTreeMap/clear.html">clear</a></li>
      <li><a href="dart-collection/SplayTreeMap/containsKey.html">containsKey</a></li>
      <li><a href="dart-collection/SplayTreeMap/containsValue.html">containsValue</a></li>
      <li><a href="dart-collection/SplayTreeMap/firstKey.html">firstKey</a></li>
      <li><a href="dart-collection/SplayTreeMap/firstKeyAfter.html">firstKeyAfter</a></li>
      <li><a href="dart-collection/SplayTreeMap/forEach.html">forEach</a></li>
      <li><a href="dart-collection/SplayTreeMap/lastKey.html">lastKey</a></li>
      <li><a href="dart-collection/SplayTreeMap/lastKeyBefore.html">lastKeyBefore</a></li>
      <li><a href="dart-collection/SplayTreeMap/putIfAbsent.html">putIfAbsent</a></li>
      <li><a href="dart-collection/SplayTreeMap/remove.html">remove</a></li>
      <li><a href="dart-collection/SplayTreeMap/update.html">update</a></li>
      <li><a href="dart-collection/SplayTreeMap/updateAll.html">updateAll</a></li>
      <li class="inherited"><a href="dart-collection/MapMixin/addEntries.html">addEntries</a></li>
      <li class="inherited"><a href="dart-collection/MapMixin/cast.html">cast</a></li>
      <li class="inherited"><a href="dart-collection/MapMixin/map.html">map</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
      <li class="inherited"><a href="dart-collection/MapMixin/removeWhere.html">removeWhere</a></li>
      <li class="inherited"><a href="dart-collection/MapMixin/toString.html">toString</a></li>
    
      <li class="section-title"><a href="dart-collection/SplayTreeMap-class.html#operators">Operators</a></li>
      <li><a href="dart-collection/SplayTreeMap/operator_get.html">operator []</a></li>
      <li><a href="dart-collection/SplayTreeMap/operator_put.html">operator []=</a></li>
      <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
