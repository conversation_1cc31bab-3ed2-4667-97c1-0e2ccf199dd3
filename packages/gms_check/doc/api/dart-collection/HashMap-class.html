<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the HashMap class from the dart:collection library, for the Dart programming language.">
  <title>HashMap class - dart:collection library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
    <li class="self-crumb">HashMap<span class="signature">&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span> abstract class</li>
  </ol>
  <div class="self-name">HashMap</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
      <li class="self-crumb">HashMap<span class="signature">&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span> abstract class</li>
    </ol>
    
    <h5>dart:collection library</h5>
    <ol>
      <li class="section-title"><a href="dart-collection/dart-collection-library.html#classes">Classes</a></li>
      <li><a href="dart-collection/DoubleLinkedQueue-class.html">DoubleLinkedQueue</a></li>
      <li><a href="dart-collection/DoubleLinkedQueueEntry-class.html">DoubleLinkedQueueEntry</a></li>
      <li><a href="dart-collection/HashMap-class.html">HashMap</a></li>
      <li><a href="dart-collection/HashSet-class.html">HashSet</a></li>
      <li><a href="dart-collection/HasNextIterator-class.html">HasNextIterator</a></li>
      <li><a href="dart-collection/IterableBase-class.html">IterableBase</a></li>
      <li><a href="dart-collection/IterableMixin-class.html">IterableMixin</a></li>
      <li><a href="dart-collection/LinkedHashMap-class.html">LinkedHashMap</a></li>
      <li><a href="dart-collection/LinkedHashSet-class.html">LinkedHashSet</a></li>
      <li><a href="dart-collection/LinkedList-class.html">LinkedList</a></li>
      <li><a href="dart-collection/LinkedListEntry-class.html">LinkedListEntry</a></li>
      <li><a href="dart-collection/ListBase-class.html">ListBase</a></li>
      <li><a href="dart-collection/ListMixin-class.html">ListMixin</a></li>
      <li><a href="dart-collection/ListQueue-class.html">ListQueue</a></li>
      <li><a href="dart-collection/MapBase-class.html">MapBase</a></li>
      <li><a href="dart-collection/MapMixin-class.html">MapMixin</a></li>
      <li><a href="dart-collection/MapView-class.html">MapView</a></li>
      <li><a href="dart-collection/Queue-class.html">Queue</a></li>
      <li><a href="dart-collection/SetBase-class.html">SetBase</a></li>
      <li><a href="dart-collection/SetMixin-class.html">SetMixin</a></li>
      <li><a href="dart-collection/SplayTreeMap-class.html">SplayTreeMap</a></li>
      <li><a href="dart-collection/SplayTreeSet-class.html">SplayTreeSet</a></li>
      <li><a href="dart-collection/UnmodifiableListView-class.html">UnmodifiableListView</a></li>
      <li><a href="dart-collection/UnmodifiableMapBase-class.html">UnmodifiableMapBase</a></li>
      <li><a href="dart-collection/UnmodifiableMapView-class.html">UnmodifiableMapView</a></li>
      <li><a href="dart-collection/UnmodifiableSetView-class.html">UnmodifiableSetView</a></li>
    
    
    
    
    
    
    
    
    </ol>
  </div>

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-class">HashMap&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span> class </h1></div>

    <section class="desc markdown">
      <p>A hash-table based implementation of <a href="dart-core/Map-class.html">Map</a>.</p>
<p>The keys of a <code>HashMap</code> must have consistent <a href="dart-core/Object/operator_equals.html">Object.==</a>
and <a href="dart-core/Object/hashCode.html">Object.hashCode</a> implementations. This means that the <code>==</code> operator
must define a stable equivalence relation on the keys (reflexive,
symmetric, transitive, and consistent over time), and that <code>hashCode</code>
must be the same for objects that are considered equal by <code>==</code>.</p>
<p>Iterating the map's keys, values or entries (through <a href="dart-core/Map/forEach.html">forEach</a>)
may happen in any order.
The iteration order only changes when the map is modified.
Values are iterated in the same order as their associated keys,
so iterating the <a href="dart-core/Map/keys.html">keys</a> and <a href="dart-core/Map/values.html">values</a> in parallel
will give matching key and value pairs.</p>
    </section>
    
    <section>
      <dl class="dl-horizontal">

        <dt>Implemented types</dt>
        <dd>
          <ul class="comma-separated clazz-relationships">
            <li><a href="dart-core/Map-class.html">Map</a><span class="signature">&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span></li>
          </ul>
        </dd>




      </dl>
    </section>

    <section class="summary offset-anchor" id="constructors">
      <h2>Constructors</h2>

      <dl class="constructor-summary-list">
        <dt id="HashMap" class="callable">
          <span class="name"><a href="dart-collection/HashMap/HashMap.html">HashMap</a></span><span class="signature">({<span class="parameter" id="-param-equals"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">equals</span>(<span class="parameter" id="param-"><span class="type-annotation">K</span></span> <span class="parameter" id="param-"><span class="type-annotation">K</span></span>), </span> <span class="parameter" id="-param-hashCode"><span class="type-annotation"><a href="dart-core/int-class.html">int</a></span> <span class="parameter-name">hashCode</span>(<span class="parameter" id="param-"><span class="type-annotation">K</span></span>), </span> <span class="parameter" id="-param-isValidKey"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">isValidKey</span>(<span class="parameter" id="param-"><span class="type-annotation">dynamic</span></span>)</span> })</span>
        </dt>
        <dd>
          Creates an unordered hash-table based <a href="dart-core/Map-class.html">Map</a>. <a href="dart-collection/HashMap/HashMap.html">[...]</a>
          <div class="constructor-modifier features">factory</div>
        </dd>
        <dt id="HashMap.from" class="callable">
          <span class="name"><a href="dart-collection/HashMap/HashMap.from.html">HashMap.from</a></span><span class="signature">(<span class="parameter" id="from-param-other"><span class="type-annotation"><a href="dart-core/Map-class.html">Map</a></span> <span class="parameter-name">other</span></span>)</span>
        </dt>
        <dd>
          Creates a <a href="dart-collection/HashMap-class.html">HashMap</a> that contains all key/value pairs of <code>other</code>. <a href="dart-collection/HashMap/HashMap.from.html">[...]</a>
          <div class="constructor-modifier features">factory</div>
        </dd>
        <dt id="HashMap.fromEntries" class="callable">
          <span class="name"><a href="dart-collection/HashMap/HashMap.fromEntries.html">HashMap.fromEntries</a></span><span class="signature">(<span class="parameter" id="fromEntries-param-entries"><span class="type-annotation"><a href="dart-core/Iterable-class.html">Iterable</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-core/MapEntry-class.html">MapEntry</a><span class="signature">&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span></span>&gt;</span></span> <span class="parameter-name">entries</span></span>)</span>
        </dt>
        <dd>
          Creates a <a href="dart-collection/HashMap-class.html">HashMap</a> containing the entries of <code>entries</code>. <a href="dart-collection/HashMap/HashMap.fromEntries.html">[...]</a>
          <div class="constructor-modifier features">factory</div>
        </dd>
        <dt id="HashMap.fromIterable" class="callable">
          <span class="name"><a href="dart-collection/HashMap/HashMap.fromIterable.html">HashMap.fromIterable</a></span><span class="signature">(<span class="parameter" id="fromIterable-param-iterable"><span class="type-annotation"><a href="dart-core/Iterable-class.html">Iterable</a></span> <span class="parameter-name">iterable</span>, {</span> <span class="parameter" id="fromIterable-param-key"><span class="type-annotation">K</span> <span class="parameter-name">key</span>(<span class="parameter" id="param-element"><span class="type-annotation">dynamic</span> <span class="parameter-name">element</span></span>), </span> <span class="parameter" id="fromIterable-param-value"><span class="type-annotation">V</span> <span class="parameter-name">value</span>(<span class="parameter" id="param-element"><span class="type-annotation">dynamic</span> <span class="parameter-name">element</span></span>)</span> })</span>
        </dt>
        <dd>
          Creates a <a href="dart-collection/HashMap-class.html">HashMap</a> where the keys and values are computed from the
<code>iterable</code>. <a href="dart-collection/HashMap/HashMap.fromIterable.html">[...]</a>
          <div class="constructor-modifier features">factory</div>
        </dd>
        <dt id="HashMap.fromIterables" class="callable">
          <span class="name"><a href="dart-collection/HashMap/HashMap.fromIterables.html">HashMap.fromIterables</a></span><span class="signature">(<span class="parameter" id="fromIterables-param-keys"><span class="type-annotation"><a href="dart-core/Iterable-class.html">Iterable</a><span class="signature">&lt;<wbr><span class="type-parameter">K</span>&gt;</span></span> <span class="parameter-name">keys</span>, </span> <span class="parameter" id="fromIterables-param-values"><span class="type-annotation"><a href="dart-core/Iterable-class.html">Iterable</a><span class="signature">&lt;<wbr><span class="type-parameter">V</span>&gt;</span></span> <span class="parameter-name">values</span></span>)</span>
        </dt>
        <dd>
          Creates a <a href="dart-collection/HashMap-class.html">HashMap</a> associating the given <code>keys</code> to <code>values</code>. <a href="dart-collection/HashMap/HashMap.fromIterables.html">[...]</a>
          <div class="constructor-modifier features">factory</div>
        </dd>
        <dt id="HashMap.identity" class="callable">
          <span class="name"><a href="dart-collection/HashMap/HashMap.identity.html">HashMap.identity</a></span><span class="signature">()</span>
        </dt>
        <dd>
          Creates an unordered identity-based map. <a href="dart-collection/HashMap/HashMap.identity.html">[...]</a>
          <div class="constructor-modifier features">factory</div>
        </dd>
        <dt id="HashMap.of" class="callable">
          <span class="name"><a href="dart-collection/HashMap/HashMap.of.html">HashMap.of</a></span><span class="signature">(<span class="parameter" id="of-param-other"><span class="type-annotation"><a href="dart-core/Map-class.html">Map</a><span class="signature">&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span></span> <span class="parameter-name">other</span></span>)</span>
        </dt>
        <dd>
          Creates a <a href="dart-collection/HashMap-class.html">HashMap</a> that contains all key/value pairs of <code>other</code>.
          <div class="constructor-modifier features">factory</div>
        </dd>
      </dl>
    </section>

    <section class="summary offset-anchor inherited" id="instance-properties">
      <h2>Properties</h2>

      <dl class="properties">
        <dt id="entries" class="property inherited">
          <span class="name"><a href="dart-core/Map/entries.html">entries</a></span>
          <span class="signature">&#8594; <a href="dart-core/Iterable-class.html">Iterable</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-core/MapEntry-class.html">MapEntry</a><span class="signature">&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span></span>&gt;</span></span>         
        </dt>
        <dd class="inherited">
          The map entries of <a href="dart-collection/HashMap-class.html">this</a>.
                  <div class="features">read-only, inherited</div>
</dd>
        <dt id="hashCode" class="property inherited">
          <span class="name"><a href="dart-core/Object/hashCode.html">hashCode</a></span>
          <span class="signature">&#8594; <a href="dart-core/int-class.html">int</a></span>         
        </dt>
        <dd class="inherited">
          The hash code for this object. <a href="dart-core/Object/hashCode.html">[...]</a>
                  <div class="features">read-only, inherited</div>
</dd>
        <dt id="isEmpty" class="property inherited">
          <span class="name"><a href="dart-core/Map/isEmpty.html">isEmpty</a></span>
          <span class="signature">&#8594; <a href="dart-core/bool-class.html">bool</a></span>         
        </dt>
        <dd class="inherited">
          Whether there is no key/value pair in the map.
                  <div class="features">read-only, inherited</div>
</dd>
        <dt id="isNotEmpty" class="property inherited">
          <span class="name"><a href="dart-core/Map/isNotEmpty.html">isNotEmpty</a></span>
          <span class="signature">&#8594; <a href="dart-core/bool-class.html">bool</a></span>         
        </dt>
        <dd class="inherited">
          Whether there is at least one key/value pair in the map.
                  <div class="features">read-only, inherited</div>
</dd>
        <dt id="keys" class="property inherited">
          <span class="name"><a href="dart-core/Map/keys.html">keys</a></span>
          <span class="signature">&#8594; <a href="dart-core/Iterable-class.html">Iterable</a><span class="signature">&lt;<wbr><span class="type-parameter">K</span>&gt;</span></span>         
        </dt>
        <dd class="inherited">
          The keys of <a href="dart-collection/HashMap-class.html">this</a>. <a href="dart-core/Map/keys.html">[...]</a>
                  <div class="features">read-only, inherited</div>
</dd>
        <dt id="length" class="property inherited">
          <span class="name"><a href="dart-core/Map/length.html">length</a></span>
          <span class="signature">&#8594; <a href="dart-core/int-class.html">int</a></span>         
        </dt>
        <dd class="inherited">
          The number of key/value pairs in the map.
                  <div class="features">read-only, inherited</div>
</dd>
        <dt id="runtimeType" class="property inherited">
          <span class="name"><a href="dart-core/Object/runtimeType.html">runtimeType</a></span>
          <span class="signature">&#8594; <a href="dart-core/Type-class.html">Type</a></span>         
        </dt>
        <dd class="inherited">
          A representation of the runtime type of the object.
                  <div class="features">read-only, inherited</div>
</dd>
        <dt id="values" class="property inherited">
          <span class="name"><a href="dart-core/Map/values.html">values</a></span>
          <span class="signature">&#8594; <a href="dart-core/Iterable-class.html">Iterable</a><span class="signature">&lt;<wbr><span class="type-parameter">V</span>&gt;</span></span>         
        </dt>
        <dd class="inherited">
          The values of <a href="dart-collection/HashMap-class.html">this</a>. <a href="dart-core/Map/values.html">[...]</a>
                  <div class="features">read-only, inherited</div>
</dd>
      </dl>
    </section>

    <section class="summary offset-anchor inherited" id="instance-methods">
      <h2>Methods</h2>
      <dl class="callables">
        <dt id="addAll" class="callable inherited">
          <span class="name"><a href="dart-core/Map/addAll.html">addAll</a></span><span class="signature">(<wbr><span class="parameter" id="addAll-param-other"><span class="type-annotation"><a href="dart-core/Map-class.html">Map</a><span class="signature">&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span></span> <span class="parameter-name">other</span></span>)
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd class="inherited">
          Adds all key/value pairs of <code>other</code> to this map. <a href="dart-core/Map/addAll.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="addEntries" class="callable inherited">
          <span class="name"><a href="dart-core/Map/addEntries.html">addEntries</a></span><span class="signature">(<wbr><span class="parameter" id="addEntries-param-newEntries"><span class="type-annotation"><a href="dart-core/Iterable-class.html">Iterable</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-core/MapEntry-class.html">MapEntry</a><span class="signature">&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span></span>&gt;</span></span> <span class="parameter-name">newEntries</span></span>)
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd class="inherited">
          Adds all key/value pairs of <code>newEntries</code> to this map. <a href="dart-core/Map/addEntries.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="cast" class="callable inherited">
          <span class="name"><a href="dart-core/Map/cast.html">cast</a></span><span class="signature">&lt;<wbr><span class="type-parameter">RK</span>, <span class="type-parameter">RV</span>&gt;</span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-core/Map-class.html">Map</a><span class="signature">&lt;<wbr><span class="type-parameter">RK</span>, <span class="type-parameter">RV</span>&gt;</span></span>
          </span>
                  </dt>
        <dd class="inherited">
          Provides a view of this map as having <code>RK</code> keys and <code>RV</code> instances,
if necessary. <a href="dart-core/Map/cast.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="clear" class="callable inherited">
          <span class="name"><a href="dart-core/Map/clear.html">clear</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd class="inherited">
          Removes all entries from the map. <a href="dart-core/Map/clear.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="containsKey" class="callable inherited">
          <span class="name"><a href="dart-core/Map/containsKey.html">containsKey</a></span><span class="signature">(<wbr><span class="parameter" id="containsKey-param-key"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">key</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/bool-class.html">bool</a></span>
          </span>
                  </dt>
        <dd class="inherited">
          Whether this map contains the given <code>key</code>. <a href="dart-core/Map/containsKey.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="containsValue" class="callable inherited">
          <span class="name"><a href="dart-core/Map/containsValue.html">containsValue</a></span><span class="signature">(<wbr><span class="parameter" id="containsValue-param-value"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">value</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/bool-class.html">bool</a></span>
          </span>
                  </dt>
        <dd class="inherited">
          Whether this map contains the given <code>value</code>. <a href="dart-core/Map/containsValue.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="forEach" class="callable inherited">
          <span class="name"><a href="dart-core/Map/forEach.html">forEach</a></span><span class="signature">(<wbr><span class="parameter" id="forEach-param-action"><span class="type-annotation">void</span> <span class="parameter-name">action</span>(<span class="parameter" id="param-key"><span class="type-annotation">K</span> <span class="parameter-name">key</span>, </span> <span class="parameter" id="param-value"><span class="type-annotation">V</span> <span class="parameter-name">value</span></span>)</span>)
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd class="inherited">
          Applies <code>action</code> to each key/value pair of the map. <a href="dart-core/Map/forEach.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="map" class="callable inherited">
          <span class="name"><a href="dart-core/Map/map.html">map</a></span><span class="signature">&lt;<wbr><span class="type-parameter">K2</span>, <span class="type-parameter">V2</span>&gt;</span><span class="signature">(<wbr><span class="parameter" id="map-param-convert"><span class="type-annotation"><a href="dart-core/MapEntry-class.html">MapEntry</a><span class="signature">&lt;<wbr><span class="type-parameter">K2</span>, <span class="type-parameter">V2</span>&gt;</span></span> <span class="parameter-name">convert</span>(<span class="parameter" id="param-key"><span class="type-annotation">K</span> <span class="parameter-name">key</span>, </span> <span class="parameter" id="param-value"><span class="type-annotation">V</span> <span class="parameter-name">value</span></span>)</span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/Map-class.html">Map</a><span class="signature">&lt;<wbr><span class="type-parameter">K2</span>, <span class="type-parameter">V2</span>&gt;</span></span>
          </span>
                  </dt>
        <dd class="inherited">
          Returns a new map where all entries of this map are transformed by
the given <code>convert</code> function.
                  <div class="features">inherited</div>
</dd>
        <dt id="noSuchMethod" class="callable inherited">
          <span class="name"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></span><span class="signature">(<wbr><span class="parameter" id="noSuchMethod-param-invocation"><span class="type-annotation"><a href="dart-core/Invocation-class.html">Invocation</a></span> <span class="parameter-name">invocation</span></span>)
            <span class="returntype parameter">&#8594; dynamic</span>
          </span>
                  </dt>
        <dd class="inherited">
          Invoked when a non-existent method or property is accessed. <a href="dart-core/Object/noSuchMethod.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="putIfAbsent" class="callable inherited">
          <span class="name"><a href="dart-core/Map/putIfAbsent.html">putIfAbsent</a></span><span class="signature">(<wbr><span class="parameter" id="putIfAbsent-param-key"><span class="type-annotation">K</span> <span class="parameter-name">key</span>, </span> <span class="parameter" id="putIfAbsent-param-ifAbsent"><span class="type-annotation">V</span> <span class="parameter-name">ifAbsent</span>()</span>)
            <span class="returntype parameter">&#8594; V</span>
          </span>
                  </dt>
        <dd class="inherited">
          Look up the value of <code>key</code>, or add a new entry if it isn't there. <a href="dart-core/Map/putIfAbsent.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="remove" class="callable inherited">
          <span class="name"><a href="dart-core/Map/remove.html">remove</a></span><span class="signature">(<wbr><span class="parameter" id="remove-param-key"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">key</span></span>)
            <span class="returntype parameter">&#8594; V</span>
          </span>
                  </dt>
        <dd class="inherited">
          Removes <code>key</code> and its associated value, if present, from the map. <a href="dart-core/Map/remove.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="removeWhere" class="callable inherited">
          <span class="name"><a href="dart-core/Map/removeWhere.html">removeWhere</a></span><span class="signature">(<wbr><span class="parameter" id="removeWhere-param-test"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">test</span>(<span class="parameter" id="param-key"><span class="type-annotation">K</span> <span class="parameter-name">key</span>, </span> <span class="parameter" id="param-value"><span class="type-annotation">V</span> <span class="parameter-name">value</span></span>)</span>)
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd class="inherited">
          Removes all entries of this map that satisfy the given <code>test</code>.
                  <div class="features">inherited</div>
</dd>
        <dt id="toString" class="callable inherited">
          <span class="name"><a href="dart-core/Object/toString.html">toString</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-core/String-class.html">String</a></span>
          </span>
                  </dt>
        <dd class="inherited">
          A string representation of this object. <a href="dart-core/Object/toString.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="update" class="callable inherited">
          <span class="name"><a href="dart-core/Map/update.html">update</a></span><span class="signature">(<wbr><span class="parameter" id="update-param-key"><span class="type-annotation">K</span> <span class="parameter-name">key</span>, </span> <span class="parameter" id="update-param-update"><span class="type-annotation">V</span> <span class="parameter-name">update</span>(<span class="parameter" id="param-value"><span class="type-annotation">V</span> <span class="parameter-name">value</span></span>), {</span> <span class="parameter" id="update-param-ifAbsent"><span class="type-annotation">V</span> <span class="parameter-name">ifAbsent</span>()</span> })
            <span class="returntype parameter">&#8594; V</span>
          </span>
                  </dt>
        <dd class="inherited">
          Updates the value for the provided <code>key</code>. <a href="dart-core/Map/update.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="updateAll" class="callable inherited">
          <span class="name"><a href="dart-core/Map/updateAll.html">updateAll</a></span><span class="signature">(<wbr><span class="parameter" id="updateAll-param-update"><span class="type-annotation">V</span> <span class="parameter-name">update</span>(<span class="parameter" id="param-key"><span class="type-annotation">K</span> <span class="parameter-name">key</span>, </span> <span class="parameter" id="param-value"><span class="type-annotation">V</span> <span class="parameter-name">value</span></span>)</span>)
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd class="inherited">
          Updates all values. <a href="dart-core/Map/updateAll.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
      </dl>
    </section>

    <section class="summary offset-anchor inherited" id="operators">
      <h2>Operators</h2>
      <dl class="callables">
        <dt id="operator ==" class="callable inherited">
          <span class="name"><a href="dart-core/Object/operator_equals.html">operator ==</a></span><span class="signature">(<wbr><span class="parameter" id="==-param-other"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">other</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/bool-class.html">bool</a></span>
          </span>
                  </dt>
        <dd class="inherited">
          The equality operator. <a href="dart-core/Object/operator_equals.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="operator []" class="callable inherited">
          <span class="name"><a href="dart-core/Map/operator_get.html">operator []</a></span><span class="signature">(<wbr><span class="parameter" id="[]-param-key"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">key</span></span>)
            <span class="returntype parameter">&#8594; V</span>
          </span>
                  </dt>
        <dd class="inherited">
          The value for the given <code>key</code>, or <code>null</code> if <code>key</code> is not in the map. <a href="dart-core/Map/operator_get.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="operator []=" class="callable inherited">
          <span class="name"><a href="dart-core/Map/operator_put.html">operator []=</a></span><span class="signature">(<wbr><span class="parameter" id="[]=-param-key"><span class="type-annotation">K</span> <span class="parameter-name">key</span>, </span> <span class="parameter" id="[]=-param-value"><span class="type-annotation">V</span> <span class="parameter-name">value</span></span>)
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd class="inherited">
          Associates the <code>key</code> with the given <code>value</code>. <a href="dart-core/Map/operator_put.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
      </dl>
    </section>




  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
    <ol>
    
      <li class="section-title"><a href="dart-collection/HashMap-class.html#constructors">Constructors</a></li>
      <li><a href="dart-collection/HashMap/HashMap.html">HashMap</a></li>
      <li><a href="dart-collection/HashMap/HashMap.from.html">from</a></li>
      <li><a href="dart-collection/HashMap/HashMap.fromEntries.html">fromEntries</a></li>
      <li><a href="dart-collection/HashMap/HashMap.fromIterable.html">fromIterable</a></li>
      <li><a href="dart-collection/HashMap/HashMap.fromIterables.html">fromIterables</a></li>
      <li><a href="dart-collection/HashMap/HashMap.identity.html">identity</a></li>
      <li><a href="dart-collection/HashMap/HashMap.of.html">of</a></li>
    
      <li class="section-title inherited">
        <a href="dart-collection/HashMap-class.html#instance-properties">Properties</a>
      </li>
      <li class="inherited"><a href="dart-core/Map/entries.html">entries</a></li>
      <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
      <li class="inherited"><a href="dart-core/Map/isEmpty.html">isEmpty</a></li>
      <li class="inherited"><a href="dart-core/Map/isNotEmpty.html">isNotEmpty</a></li>
      <li class="inherited"><a href="dart-core/Map/keys.html">keys</a></li>
      <li class="inherited"><a href="dart-core/Map/length.html">length</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
      <li class="inherited"><a href="dart-core/Map/values.html">values</a></li>
    
      <li class="section-title inherited"><a href="dart-collection/HashMap-class.html#instance-methods">Methods</a></li>
      <li class="inherited"><a href="dart-core/Map/addAll.html">addAll</a></li>
      <li class="inherited"><a href="dart-core/Map/addEntries.html">addEntries</a></li>
      <li class="inherited"><a href="dart-core/Map/cast.html">cast</a></li>
      <li class="inherited"><a href="dart-core/Map/clear.html">clear</a></li>
      <li class="inherited"><a href="dart-core/Map/containsKey.html">containsKey</a></li>
      <li class="inherited"><a href="dart-core/Map/containsValue.html">containsValue</a></li>
      <li class="inherited"><a href="dart-core/Map/forEach.html">forEach</a></li>
      <li class="inherited"><a href="dart-core/Map/map.html">map</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
      <li class="inherited"><a href="dart-core/Map/putIfAbsent.html">putIfAbsent</a></li>
      <li class="inherited"><a href="dart-core/Map/remove.html">remove</a></li>
      <li class="inherited"><a href="dart-core/Map/removeWhere.html">removeWhere</a></li>
      <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
      <li class="inherited"><a href="dart-core/Map/update.html">update</a></li>
      <li class="inherited"><a href="dart-core/Map/updateAll.html">updateAll</a></li>
    
      <li class="section-title inherited"><a href="dart-collection/HashMap-class.html#operators">Operators</a></li>
      <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
      <li class="inherited"><a href="dart-core/Map/operator_get.html">operator []</a></li>
      <li class="inherited"><a href="dart-core/Map/operator_put.html">operator []=</a></li>
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
