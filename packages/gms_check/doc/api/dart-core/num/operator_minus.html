<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the operator - method from the num class, for the Dart programming language.">
  <title>operator - method - num class - dart:core library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
    <li><a href="dart-core/num-class.html">num</a></li>
    <li class="self-crumb">operator - abstract method</li>
  </ol>
  <div class="self-name">operator -</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
      <li><a href="dart-core/num-class.html">num</a></li>
      <li class="self-crumb">operator - abstract method</li>
    </ol>
    
    <h5>num class</h5>
    <ol>
    
        <li class="section-title"><a href="dart-core/num-class.html#constructors">Constructors</a></li>
        <li><a href="dart-core/num/num.html">num</a></li>
    
        <li class="section-title">
            <a href="dart-core/num-class.html#instance-properties">Properties</a>
        </li>
        <li><a href="dart-core/num/hashCode.html">hashCode</a></li>
        <li><a href="dart-core/num/isFinite.html">isFinite</a></li>
        <li><a href="dart-core/num/isInfinite.html">isInfinite</a></li>
        <li><a href="dart-core/num/isNaN.html">isNaN</a></li>
        <li><a href="dart-core/num/isNegative.html">isNegative</a></li>
        <li><a href="dart-core/num/sign.html">sign</a></li>
        <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
        <li class="section-title"><a href="dart-core/num-class.html#instance-methods">Methods</a></li>
        <li><a href="dart-core/num/abs.html">abs</a></li>
        <li><a href="dart-core/num/ceil.html">ceil</a></li>
        <li><a href="dart-core/num/ceilToDouble.html">ceilToDouble</a></li>
        <li><a href="dart-core/num/clamp.html">clamp</a></li>
        <li><a href="dart-core/num/compareTo.html">compareTo</a></li>
        <li><a href="dart-core/num/floor.html">floor</a></li>
        <li><a href="dart-core/num/floorToDouble.html">floorToDouble</a></li>
        <li><a href="dart-core/num/remainder.html">remainder</a></li>
        <li><a href="dart-core/num/round.html">round</a></li>
        <li><a href="dart-core/num/roundToDouble.html">roundToDouble</a></li>
        <li><a href="dart-core/num/toDouble.html">toDouble</a></li>
        <li><a href="dart-core/num/toInt.html">toInt</a></li>
        <li><a href="dart-core/num/toString.html">toString</a></li>
        <li><a href="dart-core/num/toStringAsExponential.html">toStringAsExponential</a></li>
        <li><a href="dart-core/num/toStringAsFixed.html">toStringAsFixed</a></li>
        <li><a href="dart-core/num/toStringAsPrecision.html">toStringAsPrecision</a></li>
        <li><a href="dart-core/num/truncate.html">truncate</a></li>
        <li><a href="dart-core/num/truncateToDouble.html">truncateToDouble</a></li>
        <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
    
        <li class="section-title"><a href="dart-core/num-class.html#operators">Operators</a></li>
        <li><a href="dart-core/num/operator_modulo.html">operator %</a></li>
        <li><a href="dart-core/num/operator_multiply.html">operator *</a></li>
        <li><a href="dart-core/num/operator_plus.html">operator +</a></li>
        <li><a href="dart-core/num/operator_minus.html">operator -</a></li>
        <li><a href="dart-core/num/operator_divide.html">operator /</a></li>
        <li><a href="dart-core/num/operator_less.html">operator <</a></li>
        <li><a href="dart-core/num/operator_less_equal.html">operator <=</a></li>
        <li><a href="dart-core/num/operator_equals.html">operator ==</a></li>
        <li><a href="dart-core/num/operator_greater.html">operator ></a></li>
        <li><a href="dart-core/num/operator_greater_equal.html">operator >=</a></li>
        <li><a href="dart-core/num/operator_unary_minus.html">operator unary-</a></li>
        <li><a href="dart-core/num/operator_truncate_divide.html">operator ~/</a></li>
    
    
        <li class="section-title"><a href="dart-core/num-class.html#static-methods">Static methods</a></li>
        <li><a href="dart-core/num/parse.html">parse</a></li>
        <li><a href="dart-core/num/tryParse.html">tryParse</a></li>
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-method">operator -</span> method</h1></div>

    <section class="multi-line-signature">
      <span class="returntype"><a href="dart-core/num-class.html">num</a></span>
            <span class="name ">operator -</span>
(<wbr><span class="parameter" id="--param-other"><span class="type-annotation"><a href="dart-core/num-class.html">num</a></span> <span class="parameter-name">other</span></span>)
      
    </section>
    <section class="desc markdown">
      <p>Subtracts <code>other</code> from this number.</p>
<p>The result is an <a href="dart-core/int-class.html">int</a>, as described by <a href="dart-core/num/operator_minus.html">int.-</a>,
if both this number and <code>other</code> is an integer,
otherwise the result is a <a href="dart-core/double-class.html">double</a>.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">num operator -(num other);</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
