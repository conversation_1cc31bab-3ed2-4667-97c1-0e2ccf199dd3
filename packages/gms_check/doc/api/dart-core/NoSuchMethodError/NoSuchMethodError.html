<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the NoSuchMethodError constructor from the Class NoSuchMethodError class from the dart:core library, for the Dart programming language.">
  <title>NoSuchMethodError constructor - NoSuchMethodError class - dart:core library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
    <li><a href="dart-core/NoSuchMethodError-class.html">NoSuchMethodError</a></li>
    <li class="self-crumb"><span class="deprecated">NoSuchMethodError</span> constructor</li>
  </ol>
  <div class="self-name">NoSuchMethodError</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
      <li><a href="dart-core/NoSuchMethodError-class.html">NoSuchMethodError</a></li>
      <li class="self-crumb"><span class="deprecated">NoSuchMethodError</span> constructor</li>
    </ol>
    
    <h5>NoSuchMethodError class</h5>
    <ol>
    
      <li class="section-title"><a href="dart-core/NoSuchMethodError-class.html#constructors">Constructors</a></li>
      <li><a class="deprecated" href="dart-core/NoSuchMethodError/NoSuchMethodError.html">NoSuchMethodError</a></li>
      <li><a href="dart-core/NoSuchMethodError/NoSuchMethodError.withInvocation.html">withInvocation</a></li>
    
      <li class="section-title inherited">
        <a href="dart-core/NoSuchMethodError-class.html#instance-properties">Properties</a>
      </li>
      <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
      <li class="inherited"><a href="dart-core/Error/stackTrace.html">stackTrace</a></li>
    
      <li class="section-title"><a href="dart-core/NoSuchMethodError-class.html#instance-methods">Methods</a></li>
      <li><a href="dart-core/NoSuchMethodError/toString.html">toString</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
    
      <li class="section-title inherited"><a href="dart-core/NoSuchMethodError-class.html#operators">Operators</a></li>
      <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-left-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-constructor">NoSuchMethodError</span> constructor</h1></div>

    <section class="multi-line-signature">
      <div>
        <ol class="annotation-list">
          <li>@<a href="dart-core/Deprecated-class.html">Deprecated</a>(&quot;Use NoSuchMethod.withInvocation instead&quot;)</li>
        </ol>
      </div>
      
      <span class="name deprecated">NoSuchMethodError</span>(<wbr><span class="parameter" id="-param-receiver"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">receiver</span>, </span> <span class="parameter" id="-param-memberName"><span class="type-annotation"><a href="dart-core/Symbol-class.html">Symbol</a></span> <span class="parameter-name">memberName</span>, </span> <span class="parameter" id="-param-positionalArguments"><span class="type-annotation"><a href="dart-core/List-class.html">List</a></span> <span class="parameter-name">positionalArguments</span>, </span> <span class="parameter" id="-param-namedArguments"><span class="type-annotation"><a href="dart-core/Map-class.html">Map</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-core/Symbol-class.html">Symbol</a></span>, <span class="type-parameter">dynamic</span>&gt;</span></span> <span class="parameter-name">namedArguments</span></span>)
    </section>

    <section class="desc markdown">
      <p>Create a <a href="dart-core/NoSuchMethodError-class.html">NoSuchMethodError</a> corresponding to a failed method call.</p>
<p>The <code>receiver</code> is the receiver of the method call.
That is, the object on which the method was attempted called.
If the receiver is <code>null</code>, it is interpreted as a call to a top-level
function of a library.</p>
<p>The <code>memberName</code> is a <a href="dart-core/Symbol-class.html">Symbol</a> representing the name of the called method
or accessor.</p>
<p>The <code>positionalArguments</code> is a list of the positional arguments that the
method was called with. If <code>null</code>, it is considered equivalent to the
empty list.</p>
<p>The <code>namedArguments</code> is a map from <a href="dart-core/Symbol-class.html">Symbol</a>s to the values of named
arguments that the method was called with. If <code>null</code>, it is considered
equivalent to the empty map.</p>
<p>This constructor does not handle type arguments.
To include type variables, create an <a href="dart-core/Invocation-class.html">Invocation</a> and use
<a href="dart-core/NoSuchMethodError/NoSuchMethodError.withInvocation.html">NoSuchMethodError.withInvocation</a>.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">@Deprecated(&quot;Use NoSuchMethod.withInvocation instead&quot;)
external NoSuchMethodError(Object? receiver, Symbol memberName,
    List? positionalArguments, Map&lt;Symbol, dynamic&gt;? namedArguments);</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
