<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the toList method from the Iterable class, for the Dart programming language.">
  <title>toList method - Iterable class - dart:core library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
    <li><a href="dart-core/Iterable-class.html">Iterable<span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></a></li>
    <li class="self-crumb">toList method</li>
  </ol>
  <div class="self-name">toList</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
      <li><a href="dart-core/Iterable-class.html">Iterable<span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></a></li>
      <li class="self-crumb">toList method</li>
    </ol>
    
    <h5>Iterable class</h5>
    <ol>
    
        <li class="section-title"><a href="dart-core/Iterable-class.html#constructors">Constructors</a></li>
        <li><a href="dart-core/Iterable/Iterable.html">Iterable</a></li>
        <li><a href="dart-core/Iterable/Iterable.empty.html">empty</a></li>
        <li><a href="dart-core/Iterable/Iterable.generate.html">generate</a></li>
    
        <li class="section-title">
            <a href="dart-core/Iterable-class.html#instance-properties">Properties</a>
        </li>
        <li><a href="dart-core/Iterable/first.html">first</a></li>
        <li><a href="dart-core/Iterable/isEmpty.html">isEmpty</a></li>
        <li><a href="dart-core/Iterable/isNotEmpty.html">isNotEmpty</a></li>
        <li><a href="dart-core/Iterable/iterator.html">iterator</a></li>
        <li><a href="dart-core/Iterable/last.html">last</a></li>
        <li><a href="dart-core/Iterable/length.html">length</a></li>
        <li><a href="dart-core/Iterable/single.html">single</a></li>
        <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
        <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
        <li class="section-title"><a href="dart-core/Iterable-class.html#instance-methods">Methods</a></li>
        <li><a href="dart-core/Iterable/any.html">any</a></li>
        <li><a href="dart-core/Iterable/cast.html">cast</a></li>
        <li><a href="dart-core/Iterable/contains.html">contains</a></li>
        <li><a href="dart-core/Iterable/elementAt.html">elementAt</a></li>
        <li><a href="dart-core/Iterable/every.html">every</a></li>
        <li><a href="dart-core/Iterable/expand.html">expand</a></li>
        <li><a href="dart-core/Iterable/firstWhere.html">firstWhere</a></li>
        <li><a href="dart-core/Iterable/fold.html">fold</a></li>
        <li><a href="dart-core/Iterable/followedBy.html">followedBy</a></li>
        <li><a href="dart-core/Iterable/forEach.html">forEach</a></li>
        <li><a href="dart-core/Iterable/join.html">join</a></li>
        <li><a href="dart-core/Iterable/lastWhere.html">lastWhere</a></li>
        <li><a href="dart-core/Iterable/map.html">map</a></li>
        <li><a href="dart-core/Iterable/reduce.html">reduce</a></li>
        <li><a href="dart-core/Iterable/singleWhere.html">singleWhere</a></li>
        <li><a href="dart-core/Iterable/skip.html">skip</a></li>
        <li><a href="dart-core/Iterable/skipWhile.html">skipWhile</a></li>
        <li><a href="dart-core/Iterable/take.html">take</a></li>
        <li><a href="dart-core/Iterable/takeWhile.html">takeWhile</a></li>
        <li><a href="dart-core/Iterable/toList.html">toList</a></li>
        <li><a href="dart-core/Iterable/toSet.html">toSet</a></li>
        <li><a href="dart-core/Iterable/toString.html">toString</a></li>
        <li><a href="dart-core/Iterable/where.html">where</a></li>
        <li><a href="dart-core/Iterable/whereType.html">whereType</a></li>
        <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
    
        <li class="section-title inherited"><a href="dart-core/Iterable-class.html#operators">Operators</a></li>
        <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
        <li class="section-title"><a href="dart-core/Iterable-class.html#static-methods">Static methods</a></li>
        <li><a href="dart-core/Iterable/castFrom.html">castFrom</a></li>
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-method">toList</span> method</h1></div>

    <section class="multi-line-signature">
      <span class="returntype"><a href="dart-core/List-class.html">List</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span>
            <span class="name ">toList</span>
(<wbr>{<span class="parameter" id="toList-param-growable"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">growable</span>: <span class="default-value">true</span></span> })
      
    </section>
    <section class="desc markdown">
      <p>Creates a <a href="dart-core/List-class.html">List</a> containing the elements of this <a href="dart-core/Iterable-class.html">Iterable</a>.</p>
<p>The elements are in iteration order.
The list is fixed-length if <code>growable</code> is false.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">List&lt;E&gt; toList({bool growable = true}) {
  return List&lt;E&gt;.of(this, growable: growable);
}</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
