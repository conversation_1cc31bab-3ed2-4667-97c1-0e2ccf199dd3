<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the cast method from the Set class, for the Dart programming language.">
  <title>cast method - Set class - dart:core library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
    <li><a href="dart-core/Set-class.html">Set<span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></a></li>
    <li class="self-crumb">cast&lt;<wbr><span class="type-parameter">R</span>&gt; abstract method</li>
  </ol>
  <div class="self-name">cast</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
      <li><a href="dart-core/Set-class.html">Set<span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></a></li>
      <li class="self-crumb">cast&lt;<wbr><span class="type-parameter">R</span>&gt; abstract method</li>
    </ol>
    
    <h5>Set class</h5>
    <ol>
    
        <li class="section-title"><a href="dart-core/Set-class.html#constructors">Constructors</a></li>
        <li><a href="dart-core/Set/Set.html">Set</a></li>
        <li><a href="dart-core/Set/Set.from.html">from</a></li>
        <li><a href="dart-core/Set/Set.identity.html">identity</a></li>
        <li><a href="dart-core/Set/Set.of.html">of</a></li>
        <li><a href="dart-core/Set/Set.unmodifiable.html">unmodifiable</a></li>
    
        <li class="section-title">
            <a href="dart-core/Set-class.html#instance-properties">Properties</a>
        </li>
        <li><a href="dart-core/Set/iterator.html">iterator</a></li>
        <li class="inherited"><a href="dart-core/Iterable/first.html">first</a></li>
        <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
        <li class="inherited"><a href="dart-core/Iterable/isEmpty.html">isEmpty</a></li>
        <li class="inherited"><a href="dart-core/Iterable/isNotEmpty.html">isNotEmpty</a></li>
        <li class="inherited"><a href="dart-core/Iterable/last.html">last</a></li>
        <li class="inherited"><a href="dart-core/Iterable/length.html">length</a></li>
        <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
        <li class="inherited"><a href="dart-core/Iterable/single.html">single</a></li>
    
        <li class="section-title"><a href="dart-core/Set-class.html#instance-methods">Methods</a></li>
        <li><a href="dart-core/Set/add.html">add</a></li>
        <li><a href="dart-core/Set/addAll.html">addAll</a></li>
        <li><a href="dart-core/Set/cast.html">cast</a></li>
        <li><a href="dart-core/Set/clear.html">clear</a></li>
        <li><a href="dart-core/Set/contains.html">contains</a></li>
        <li><a href="dart-core/Set/containsAll.html">containsAll</a></li>
        <li><a href="dart-core/Set/difference.html">difference</a></li>
        <li><a href="dart-core/Set/intersection.html">intersection</a></li>
        <li><a href="dart-core/Set/lookup.html">lookup</a></li>
        <li><a href="dart-core/Set/remove.html">remove</a></li>
        <li><a href="dart-core/Set/removeAll.html">removeAll</a></li>
        <li><a href="dart-core/Set/removeWhere.html">removeWhere</a></li>
        <li><a href="dart-core/Set/retainAll.html">retainAll</a></li>
        <li><a href="dart-core/Set/retainWhere.html">retainWhere</a></li>
        <li><a href="dart-core/Set/toSet.html">toSet</a></li>
        <li><a href="dart-core/Set/union.html">union</a></li>
        <li class="inherited"><a href="dart-core/Iterable/any.html">any</a></li>
        <li class="inherited"><a href="dart-core/Iterable/elementAt.html">elementAt</a></li>
        <li class="inherited"><a href="dart-core/Iterable/every.html">every</a></li>
        <li class="inherited"><a href="dart-core/Iterable/expand.html">expand</a></li>
        <li class="inherited"><a href="dart-core/Iterable/firstWhere.html">firstWhere</a></li>
        <li class="inherited"><a href="dart-core/Iterable/fold.html">fold</a></li>
        <li class="inherited"><a href="dart-core/Iterable/followedBy.html">followedBy</a></li>
        <li class="inherited"><a href="dart-core/Iterable/forEach.html">forEach</a></li>
        <li class="inherited"><a href="dart-core/Iterable/join.html">join</a></li>
        <li class="inherited"><a href="dart-core/Iterable/lastWhere.html">lastWhere</a></li>
        <li class="inherited"><a href="dart-core/Iterable/map.html">map</a></li>
        <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
        <li class="inherited"><a href="dart-core/Iterable/reduce.html">reduce</a></li>
        <li class="inherited"><a href="dart-core/Iterable/singleWhere.html">singleWhere</a></li>
        <li class="inherited"><a href="dart-core/Iterable/skip.html">skip</a></li>
        <li class="inherited"><a href="dart-core/Iterable/skipWhile.html">skipWhile</a></li>
        <li class="inherited"><a href="dart-core/Iterable/take.html">take</a></li>
        <li class="inherited"><a href="dart-core/Iterable/takeWhile.html">takeWhile</a></li>
        <li class="inherited"><a href="dart-core/Iterable/toList.html">toList</a></li>
        <li class="inherited"><a href="dart-core/Iterable/toString.html">toString</a></li>
        <li class="inherited"><a href="dart-core/Iterable/where.html">where</a></li>
        <li class="inherited"><a href="dart-core/Iterable/whereType.html">whereType</a></li>
    
        <li class="section-title inherited"><a href="dart-core/Set-class.html#operators">Operators</a></li>
        <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
        <li class="section-title"><a href="dart-core/Set-class.html#static-methods">Static methods</a></li>
        <li><a href="dart-core/Set/castFrom.html">castFrom</a></li>
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-method">cast&lt;<wbr><span class="type-parameter">R</span>&gt;</span> method</h1></div>

    <section class="multi-line-signature">
      <span class="returntype"><a href="dart-core/Set-class.html">Set</a><span class="signature">&lt;<wbr><span class="type-parameter">R</span>&gt;</span></span>
            <span class="name ">cast</span>
&lt;<wbr><span class="type-parameter">R</span>&gt;(<wbr>)
      <div class="features">override</div>
    </section>
    <section class="desc markdown">
      <p>Provides a view of this set as a set of <code>R</code> instances.</p>
<p>If this set contains only instances of <code>R</code>, all read operations
will work correctly. If any operation tries to access an element
that is not an instance of <code>R</code>, the access will throw instead.</p>
<p>Elements added to the set (e.g., by using <a href="dart-core/Set/add.html">add</a> or <a href="dart-core/Set/addAll.html">addAll</a>)
must be instance of <code>R</code> to be valid arguments to the adding function,
and they must be instances of <code>E</code> as well to be accepted by
this set as well.</p>
<p>Methods like <a href="dart-core/Set/contains.html">contains</a>, <a href="dart-core/Set/remove.html">remove</a> and <a href="dart-core/Set/removeAll.html">removeAll</a>
which accept one or more <code>Object?</code> as argument,
will pass the argument directly to the this set's method
without any checks.
That means that you can do <code>setOfStrings.cast&lt;int&gt;().remove("a")</code>
successfully, even if it looks like it shouldn't have any effect.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">Set&lt;R&gt; cast&lt;R&gt;();</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
