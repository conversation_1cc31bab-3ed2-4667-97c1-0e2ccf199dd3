<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the num class from the dart:core library, for the Dart programming language.">
  <title>num class - dart:core library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
    <li class="self-crumb">num abstract class</li>
  </ol>
  <div class="self-name">num</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
      <li class="self-crumb">num abstract class</li>
    </ol>
    
    <h5>dart:core library</h5>
    <ol>
      <li class="section-title"><a href="dart-core/dart-core-library.html#classes">Classes</a></li>
      <li><a href="dart-core/BidirectionalIterator-class.html">BidirectionalIterator</a></li>
      <li><a href="dart-core/BigInt-class.html">BigInt</a></li>
      <li><a href="dart-core/bool-class.html">bool</a></li>
      <li><a href="dart-core/Comparable-class.html">Comparable</a></li>
      <li><a href="dart-core/DateTime-class.html">DateTime</a></li>
      <li><a href="dart-core/Deprecated-class.html">Deprecated</a></li>
      <li><a href="dart-core/double-class.html">double</a></li>
      <li><a href="dart-core/Duration-class.html">Duration</a></li>
      <li><a href="dart-core/Enum-class.html">Enum</a></li>
      <li><a href="dart-core/Expando-class.html">Expando</a></li>
      <li><a href="dart-core/Function-class.html">Function</a></li>
      <li><a href="dart-async/Future-class.html">Future</a></li>
      <li><a href="dart-core/int-class.html">int</a></li>
      <li><a href="dart-core/Invocation-class.html">Invocation</a></li>
      <li><a href="dart-core/Iterable-class.html">Iterable</a></li>
      <li><a href="dart-core/Iterator-class.html">Iterator</a></li>
      <li><a href="dart-core/List-class.html">List</a></li>
      <li><a href="dart-core/Map-class.html">Map</a></li>
      <li><a href="dart-core/MapEntry-class.html">MapEntry</a></li>
      <li><a href="dart-core/Match-class.html">Match</a></li>
      <li><a href="dart-core/Null-class.html">Null</a></li>
      <li><a href="dart-core/num-class.html">num</a></li>
      <li><a href="dart-core/Object-class.html">Object</a></li>
      <li><a href="dart-core/Pattern-class.html">Pattern</a></li>
      <li><a href="dart-core/pragma-class.html">pragma</a></li>
      <li><a class="deprecated" href="dart-core/Provisional-class.html">Provisional</a></li>
      <li><a href="dart-core/RegExp-class.html">RegExp</a></li>
      <li><a href="dart-core/RegExpMatch-class.html">RegExpMatch</a></li>
      <li><a href="dart-core/RuneIterator-class.html">RuneIterator</a></li>
      <li><a href="dart-core/Runes-class.html">Runes</a></li>
      <li><a href="dart-core/Set-class.html">Set</a></li>
      <li><a href="dart-core/Sink-class.html">Sink</a></li>
      <li><a href="dart-core/StackTrace-class.html">StackTrace</a></li>
      <li><a href="dart-core/Stopwatch-class.html">Stopwatch</a></li>
      <li><a href="dart-async/Stream-class.html">Stream</a></li>
      <li><a href="dart-core/String-class.html">String</a></li>
      <li><a href="dart-core/StringBuffer-class.html">StringBuffer</a></li>
      <li><a href="dart-core/StringSink-class.html">StringSink</a></li>
      <li><a href="dart-core/Symbol-class.html">Symbol</a></li>
      <li><a href="dart-core/Type-class.html">Type</a></li>
      <li><a href="dart-core/Uri-class.html">Uri</a></li>
      <li><a href="dart-core/UriData-class.html">UriData</a></li>
    
      <li class="section-title"><a href="dart-core/dart-core-library.html#extension">Extensions</a></li>
      <li><a href="dart-async/FutureExtensions.html">FutureExtensions</a></li>
    
    
      <li class="section-title"><a href="dart-core/dart-core-library.html#constants">Constants</a></li>
      <li><a href="dart-core/deprecated-constant.html">deprecated</a></li>
      <li><a href="dart-core/override-constant.html">override</a></li>
      <li><a class="deprecated" href="dart-core/provisional-constant.html">provisional</a></li>
      <li><a class="deprecated" href="dart-core/proxy-constant.html">proxy</a></li>
    
    
      <li class="section-title"><a href="dart-core/dart-core-library.html#functions">Functions</a></li>
      <li><a href="dart-core/identical.html">identical</a></li>
      <li><a href="dart-core/identityHashCode.html">identityHashCode</a></li>
      <li><a href="dart-core/print.html">print</a></li>
    
    
      <li class="section-title"><a href="dart-core/dart-core-library.html#typedefs">Typedefs</a></li>
      <li><a href="dart-core/Comparator.html">Comparator</a></li>
    
      <li class="section-title"><a href="dart-core/dart-core-library.html#exceptions">Exceptions</a></li>
      <li><a href="dart-core/AbstractClassInstantiationError-class.html">AbstractClassInstantiationError</a></li>
      <li><a href="dart-core/ArgumentError-class.html">ArgumentError</a></li>
      <li><a href="dart-core/AssertionError-class.html">AssertionError</a></li>
      <li><a class="deprecated" href="dart-core/CastError-class.html">CastError</a></li>
      <li><a href="dart-core/ConcurrentModificationError-class.html">ConcurrentModificationError</a></li>
      <li><a href="dart-core/CyclicInitializationError-class.html">CyclicInitializationError</a></li>
      <li><a href="dart-core/Error-class.html">Error</a></li>
      <li><a href="dart-core/Exception-class.html">Exception</a></li>
      <li><a href="dart-core/FallThroughError-class.html">FallThroughError</a></li>
      <li><a href="dart-core/FormatException-class.html">FormatException</a></li>
      <li><a href="dart-core/IndexError-class.html">IndexError</a></li>
      <li><a href="dart-core/IntegerDivisionByZeroException-class.html">IntegerDivisionByZeroException</a></li>
      <li><a href="dart-core/NoSuchMethodError-class.html">NoSuchMethodError</a></li>
      <li><a href="dart-core/NullThrownError-class.html">NullThrownError</a></li>
      <li><a href="dart-core/OutOfMemoryError-class.html">OutOfMemoryError</a></li>
      <li><a href="dart-core/RangeError-class.html">RangeError</a></li>
      <li><a href="dart-core/StackOverflowError-class.html">StackOverflowError</a></li>
      <li><a href="dart-core/StateError-class.html">StateError</a></li>
      <li><a href="dart-core/TypeError-class.html">TypeError</a></li>
      <li><a href="dart-core/UnimplementedError-class.html">UnimplementedError</a></li>
      <li><a href="dart-core/UnsupportedError-class.html">UnsupportedError</a></li>
    </ol>
  </div>

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-class">num</span> class </h1></div>

    <section class="desc markdown">
      <p>An integer or floating-point number.</p>
<p>It is a compile-time error for any type other than <a href="dart-core/int-class.html">int</a> or <a href="dart-core/double-class.html">double</a>
to attempt to extend or implement <code>num</code>.</p>
    </section>
    
    <section>
      <dl class="dl-horizontal">

        <dt>Implemented types</dt>
        <dd>
          <ul class="comma-separated clazz-relationships">
            <li><a href="dart-core/Comparable-class.html">Comparable</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-core/num-class.html">num</a></span>&gt;</span></li>
          </ul>
        </dd>


        <dt>Implementers</dt>
        <dd><ul class="comma-separated clazz-relationships">
          <li><a href="dart-core/double-class.html">double</a></li>
          <li><a href="dart-core/int-class.html">int</a></li>
        </ul></dd>


      </dl>
    </section>

    <section class="summary offset-anchor" id="constructors">
      <h2>Constructors</h2>

      <dl class="constructor-summary-list">
        <dt id="num" class="callable">
          <span class="name"><a href="dart-core/num/num.html">num</a></span><span class="signature">()</span>
        </dt>
        <dd>
          
        </dd>
      </dl>
    </section>

    <section class="summary offset-anchor" id="instance-properties">
      <h2>Properties</h2>

      <dl class="properties">
        <dt id="hashCode" class="property">
          <span class="name"><a href="dart-core/num/hashCode.html">hashCode</a></span>
          <span class="signature">&#8594; <a href="dart-core/int-class.html">int</a></span>         
        </dt>
        <dd>
          Returns a hash code for a numerical value. <a href="dart-core/num/hashCode.html">[...]</a>
                  <div class="features">read-only, override</div>
</dd>
        <dt id="isFinite" class="property">
          <span class="name"><a href="dart-core/num/isFinite.html">isFinite</a></span>
          <span class="signature">&#8594; <a href="dart-core/bool-class.html">bool</a></span>         
        </dt>
        <dd>
          Whether this number is finite. <a href="dart-core/num/isFinite.html">[...]</a>
                  <div class="features">read-only</div>
</dd>
        <dt id="isInfinite" class="property">
          <span class="name"><a href="dart-core/num/isInfinite.html">isInfinite</a></span>
          <span class="signature">&#8594; <a href="dart-core/bool-class.html">bool</a></span>         
        </dt>
        <dd>
          Whether this number is positive infinity or negative infinity. <a href="dart-core/num/isInfinite.html">[...]</a>
                  <div class="features">read-only</div>
</dd>
        <dt id="isNaN" class="property">
          <span class="name"><a href="dart-core/num/isNaN.html">isNaN</a></span>
          <span class="signature">&#8594; <a href="dart-core/bool-class.html">bool</a></span>         
        </dt>
        <dd>
          Whether this number is a Not-a-Number value. <a href="dart-core/num/isNaN.html">[...]</a>
                  <div class="features">read-only</div>
</dd>
        <dt id="isNegative" class="property">
          <span class="name"><a href="dart-core/num/isNegative.html">isNegative</a></span>
          <span class="signature">&#8594; <a href="dart-core/bool-class.html">bool</a></span>         
        </dt>
        <dd>
          Whether this number is negative. <a href="dart-core/num/isNegative.html">[...]</a>
                  <div class="features">read-only</div>
</dd>
        <dt id="sign" class="property">
          <span class="name"><a href="dart-core/num/sign.html">sign</a></span>
          <span class="signature">&#8594; <a href="dart-core/num-class.html">num</a></span>         
        </dt>
        <dd>
          Negative one, zero or positive one depending on the sign and
numerical value of this number. <a href="dart-core/num/sign.html">[...]</a>
                  <div class="features">read-only</div>
</dd>
        <dt id="runtimeType" class="property inherited">
          <span class="name"><a href="dart-core/Object/runtimeType.html">runtimeType</a></span>
          <span class="signature">&#8594; <a href="dart-core/Type-class.html">Type</a></span>         
        </dt>
        <dd class="inherited">
          A representation of the runtime type of the object.
                  <div class="features">read-only, inherited</div>
</dd>
      </dl>
    </section>

    <section class="summary offset-anchor" id="instance-methods">
      <h2>Methods</h2>
      <dl class="callables">
        <dt id="abs" class="callable">
          <span class="name"><a href="dart-core/num/abs.html">abs</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-core/num-class.html">num</a></span>
          </span>
                  </dt>
        <dd>
          The absolute value of this number. <a href="dart-core/num/abs.html">[...]</a>
                  
</dd>
        <dt id="ceil" class="callable">
          <span class="name"><a href="dart-core/num/ceil.html">ceil</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-core/int-class.html">int</a></span>
          </span>
                  </dt>
        <dd>
          The least integer no smaller than <code>this</code>. <a href="dart-core/num/ceil.html">[...]</a>
                  
</dd>
        <dt id="ceilToDouble" class="callable">
          <span class="name"><a href="dart-core/num/ceilToDouble.html">ceilToDouble</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-core/double-class.html">double</a></span>
          </span>
                  </dt>
        <dd>
          Returns the least double integer value no smaller than <code>this</code>. <a href="dart-core/num/ceilToDouble.html">[...]</a>
                  
</dd>
        <dt id="clamp" class="callable">
          <span class="name"><a href="dart-core/num/clamp.html">clamp</a></span><span class="signature">(<wbr><span class="parameter" id="clamp-param-lowerLimit"><span class="type-annotation"><a href="dart-core/num-class.html">num</a></span> <span class="parameter-name">lowerLimit</span></span> <span class="parameter" id="clamp-param-upperLimit"><span class="type-annotation"><a href="dart-core/num-class.html">num</a></span> <span class="parameter-name">upperLimit</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/num-class.html">num</a></span>
          </span>
                  </dt>
        <dd>
          Returns this <a href="dart-core/num-class.html">num</a> clamped to be in the range <code>lowerLimit</code>-<code>upperLimit</code>. <a href="dart-core/num/clamp.html">[...]</a>
                  
</dd>
        <dt id="compareTo" class="callable">
          <span class="name"><a href="dart-core/num/compareTo.html">compareTo</a></span><span class="signature">(<wbr><span class="parameter" id="compareTo-param-other"><span class="type-annotation"><a href="dart-core/num-class.html">num</a></span> <span class="parameter-name">other</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/int-class.html">int</a></span>
          </span>
                  </dt>
        <dd>
          Compares this to <code>other</code>. <a href="dart-core/num/compareTo.html">[...]</a>
                  <div class="features">override</div>
</dd>
        <dt id="floor" class="callable">
          <span class="name"><a href="dart-core/num/floor.html">floor</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-core/int-class.html">int</a></span>
          </span>
                  </dt>
        <dd>
          The greatest integer no greater than this number. <a href="dart-core/num/floor.html">[...]</a>
                  
</dd>
        <dt id="floorToDouble" class="callable">
          <span class="name"><a href="dart-core/num/floorToDouble.html">floorToDouble</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-core/double-class.html">double</a></span>
          </span>
                  </dt>
        <dd>
          Returns the greatest double integer value no greater than <code>this</code>. <a href="dart-core/num/floorToDouble.html">[...]</a>
                  
</dd>
        <dt id="remainder" class="callable">
          <span class="name"><a href="dart-core/num/remainder.html">remainder</a></span><span class="signature">(<wbr><span class="parameter" id="remainder-param-other"><span class="type-annotation"><a href="dart-core/num-class.html">num</a></span> <span class="parameter-name">other</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/num-class.html">num</a></span>
          </span>
                  </dt>
        <dd>
          The remainder of the truncating division of <code>this</code> by <code>other</code>. <a href="dart-core/num/remainder.html">[...]</a>
                  
</dd>
        <dt id="round" class="callable">
          <span class="name"><a href="dart-core/num/round.html">round</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-core/int-class.html">int</a></span>
          </span>
                  </dt>
        <dd>
          The integer closest to this number. <a href="dart-core/num/round.html">[...]</a>
                  
</dd>
        <dt id="roundToDouble" class="callable">
          <span class="name"><a href="dart-core/num/roundToDouble.html">roundToDouble</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-core/double-class.html">double</a></span>
          </span>
                  </dt>
        <dd>
          The double integer value closest to this value. <a href="dart-core/num/roundToDouble.html">[...]</a>
                  
</dd>
        <dt id="toDouble" class="callable">
          <span class="name"><a href="dart-core/num/toDouble.html">toDouble</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-core/double-class.html">double</a></span>
          </span>
                  </dt>
        <dd>
          This number as a <a href="dart-core/double-class.html">double</a>. <a href="dart-core/num/toDouble.html">[...]</a>
                  
</dd>
        <dt id="toInt" class="callable">
          <span class="name"><a href="dart-core/num/toInt.html">toInt</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-core/int-class.html">int</a></span>
          </span>
                  </dt>
        <dd>
          Truncates this <a href="dart-core/num-class.html">num</a> to an integer and returns the result as an <a href="dart-core/int-class.html">int</a>. <a href="dart-core/num/toInt.html">[...]</a>
                  
</dd>
        <dt id="toString" class="callable">
          <span class="name"><a href="dart-core/num/toString.html">toString</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-core/String-class.html">String</a></span>
          </span>
                  </dt>
        <dd>
          The shortest string that correctly represent this number number. <a href="dart-core/num/toString.html">[...]</a>
                  <div class="features">override</div>
</dd>
        <dt id="toStringAsExponential" class="callable">
          <span class="name"><a href="dart-core/num/toStringAsExponential.html">toStringAsExponential</a></span><span class="signature">(<wbr>[<span class="parameter" id="toStringAsExponential-param-fractionDigits"><span class="type-annotation"><a href="dart-core/int-class.html">int</a></span> <span class="parameter-name">fractionDigits</span></span> ])
            <span class="returntype parameter">&#8594; <a href="dart-core/String-class.html">String</a></span>
          </span>
                  </dt>
        <dd>
          An exponential string-representation of this number. <a href="dart-core/num/toStringAsExponential.html">[...]</a>
                  
</dd>
        <dt id="toStringAsFixed" class="callable">
          <span class="name"><a href="dart-core/num/toStringAsFixed.html">toStringAsFixed</a></span><span class="signature">(<wbr><span class="parameter" id="toStringAsFixed-param-fractionDigits"><span class="type-annotation"><a href="dart-core/int-class.html">int</a></span> <span class="parameter-name">fractionDigits</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/String-class.html">String</a></span>
          </span>
                  </dt>
        <dd>
          A decimal-point string-representation of this number. <a href="dart-core/num/toStringAsFixed.html">[...]</a>
                  
</dd>
        <dt id="toStringAsPrecision" class="callable">
          <span class="name"><a href="dart-core/num/toStringAsPrecision.html">toStringAsPrecision</a></span><span class="signature">(<wbr><span class="parameter" id="toStringAsPrecision-param-precision"><span class="type-annotation"><a href="dart-core/int-class.html">int</a></span> <span class="parameter-name">precision</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/String-class.html">String</a></span>
          </span>
                  </dt>
        <dd>
          A string representation with <code>precision</code> significant digits. <a href="dart-core/num/toStringAsPrecision.html">[...]</a>
                  
</dd>
        <dt id="truncate" class="callable">
          <span class="name"><a href="dart-core/num/truncate.html">truncate</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-core/int-class.html">int</a></span>
          </span>
                  </dt>
        <dd>
          The integer obtained by discarding any fractional digits from <code>this</code>. <a href="dart-core/num/truncate.html">[...]</a>
                  
</dd>
        <dt id="truncateToDouble" class="callable">
          <span class="name"><a href="dart-core/num/truncateToDouble.html">truncateToDouble</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-core/double-class.html">double</a></span>
          </span>
                  </dt>
        <dd>
          Returns the double integer value obtained by discarding any fractional
digits from the double value of <code>this</code>. <a href="dart-core/num/truncateToDouble.html">[...]</a>
                  
</dd>
        <dt id="noSuchMethod" class="callable inherited">
          <span class="name"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></span><span class="signature">(<wbr><span class="parameter" id="noSuchMethod-param-invocation"><span class="type-annotation"><a href="dart-core/Invocation-class.html">Invocation</a></span> <span class="parameter-name">invocation</span></span>)
            <span class="returntype parameter">&#8594; dynamic</span>
          </span>
                  </dt>
        <dd class="inherited">
          Invoked when a non-existent method or property is accessed. <a href="dart-core/Object/noSuchMethod.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
      </dl>
    </section>

    <section class="summary offset-anchor" id="operators">
      <h2>Operators</h2>
      <dl class="callables">
        <dt id="operator %" class="callable">
          <span class="name"><a href="dart-core/num/operator_modulo.html">operator %</a></span><span class="signature">(<wbr><span class="parameter" id="%-param-other"><span class="type-annotation"><a href="dart-core/num-class.html">num</a></span> <span class="parameter-name">other</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/num-class.html">num</a></span>
          </span>
                  </dt>
        <dd>
          Euclidean modulo of this number by <code>other</code>. <a href="dart-core/num/operator_modulo.html">[...]</a>
                  
</dd>
        <dt id="operator *" class="callable">
          <span class="name"><a href="dart-core/num/operator_multiply.html">operator *</a></span><span class="signature">(<wbr><span class="parameter" id="*-param-other"><span class="type-annotation"><a href="dart-core/num-class.html">num</a></span> <span class="parameter-name">other</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/num-class.html">num</a></span>
          </span>
                  </dt>
        <dd>
          Multiplies this number by <code>other</code>. <a href="dart-core/num/operator_multiply.html">[...]</a>
                  
</dd>
        <dt id="operator +" class="callable">
          <span class="name"><a href="dart-core/num/operator_plus.html">operator +</a></span><span class="signature">(<wbr><span class="parameter" id="+-param-other"><span class="type-annotation"><a href="dart-core/num-class.html">num</a></span> <span class="parameter-name">other</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/num-class.html">num</a></span>
          </span>
                  </dt>
        <dd>
          Adds <code>other</code> to this number. <a href="dart-core/num/operator_plus.html">[...]</a>
                  
</dd>
        <dt id="operator -" class="callable">
          <span class="name"><a href="dart-core/num/operator_minus.html">operator -</a></span><span class="signature">(<wbr><span class="parameter" id="--param-other"><span class="type-annotation"><a href="dart-core/num-class.html">num</a></span> <span class="parameter-name">other</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/num-class.html">num</a></span>
          </span>
                  </dt>
        <dd>
          Subtracts <code>other</code> from this number. <a href="dart-core/num/operator_minus.html">[...]</a>
                  
</dd>
        <dt id="operator &#x2F;" class="callable">
          <span class="name"><a href="dart-core/num/operator_divide.html">operator /</a></span><span class="signature">(<wbr><span class="parameter" id="/-param-other"><span class="type-annotation"><a href="dart-core/num-class.html">num</a></span> <span class="parameter-name">other</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/double-class.html">double</a></span>
          </span>
                  </dt>
        <dd>
          Divides this number by <code>other</code>.
                  
</dd>
        <dt id="operator &lt;" class="callable">
          <span class="name"><a href="dart-core/num/operator_less.html">operator <</a></span><span class="signature">(<wbr><span class="parameter" id="<-param-other"><span class="type-annotation"><a href="dart-core/num-class.html">num</a></span> <span class="parameter-name">other</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/bool-class.html">bool</a></span>
          </span>
                  </dt>
        <dd>
          Whether this number is numerically smaller than <code>other</code>. <a href="dart-core/num/operator_less.html">[...]</a>
                  
</dd>
        <dt id="operator &lt;=" class="callable">
          <span class="name"><a href="dart-core/num/operator_less_equal.html">operator <=</a></span><span class="signature">(<wbr><span class="parameter" id="<=-param-other"><span class="type-annotation"><a href="dart-core/num-class.html">num</a></span> <span class="parameter-name">other</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/bool-class.html">bool</a></span>
          </span>
                  </dt>
        <dd>
          Whether this number is numerically smaller than or equal to <code>other</code>. <a href="dart-core/num/operator_less_equal.html">[...]</a>
                  
</dd>
        <dt id="operator ==" class="callable">
          <span class="name"><a href="dart-core/num/operator_equals.html">operator ==</a></span><span class="signature">(<wbr><span class="parameter" id="==-param-other"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">other</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/bool-class.html">bool</a></span>
          </span>
                  </dt>
        <dd>
          Test whether this value is numerically equal to <code>other</code>. <a href="dart-core/num/operator_equals.html">[...]</a>
                  <div class="features">override</div>
</dd>
        <dt id="operator &gt;" class="callable">
          <span class="name"><a href="dart-core/num/operator_greater.html">operator ></a></span><span class="signature">(<wbr><span class="parameter" id=">-param-other"><span class="type-annotation"><a href="dart-core/num-class.html">num</a></span> <span class="parameter-name">other</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/bool-class.html">bool</a></span>
          </span>
                  </dt>
        <dd>
          Whether this number is numerically greater than <code>other</code>. <a href="dart-core/num/operator_greater.html">[...]</a>
                  
</dd>
        <dt id="operator &gt;=" class="callable">
          <span class="name"><a href="dart-core/num/operator_greater_equal.html">operator >=</a></span><span class="signature">(<wbr><span class="parameter" id=">=-param-other"><span class="type-annotation"><a href="dart-core/num-class.html">num</a></span> <span class="parameter-name">other</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/bool-class.html">bool</a></span>
          </span>
                  </dt>
        <dd>
          Whether this number is numerically greater than or equal to <code>other</code>. <a href="dart-core/num/operator_greater_equal.html">[...]</a>
                  
</dd>
        <dt id="operator unary-" class="callable">
          <span class="name"><a href="dart-core/num/operator_unary_minus.html">operator unary-</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-core/num-class.html">num</a></span>
          </span>
                  </dt>
        <dd>
          The negation of this value. <a href="dart-core/num/operator_unary_minus.html">[...]</a>
                  
</dd>
        <dt id="operator ~&#x2F;" class="callable">
          <span class="name"><a href="dart-core/num/operator_truncate_divide.html">operator ~/</a></span><span class="signature">(<wbr><span class="parameter" id="~/-param-other"><span class="type-annotation"><a href="dart-core/num-class.html">num</a></span> <span class="parameter-name">other</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/int-class.html">int</a></span>
          </span>
                  </dt>
        <dd>
          Truncating division operator. <a href="dart-core/num/operator_truncate_divide.html">[...]</a>
                  
</dd>
      </dl>
    </section>


    <section class="summary offset-anchor" id="static-methods">
      <h2>Static Methods</h2>
      <dl class="callables">
        <dt id="parse" class="callable">
          <span class="name"><a href="dart-core/num/parse.html">parse</a></span><span class="signature">(<wbr><span class="parameter" id="parse-param-input"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">input</span>, [</span> <span class="parameter" id="parse-param-onError"><span class="type-annotation"><a href="dart-core/num-class.html">num</a></span> <span class="parameter-name">onError</span>(<span class="parameter" id="param-input"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">input</span></span>)</span> ])
            <span class="returntype parameter">&#8594; <a href="dart-core/num-class.html">num</a></span>
          </span>
                  </dt>
        <dd>
          Parses a string containing a number literal into a number. <a href="dart-core/num/parse.html">[...]</a>
                  
</dd>
        <dt id="tryParse" class="callable">
          <span class="name"><a href="dart-core/num/tryParse.html">tryParse</a></span><span class="signature">(<wbr><span class="parameter" id="tryParse-param-input"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">input</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/num-class.html">num</a></span>
          </span>
                  </dt>
        <dd>
          Parses a string containing a number literal into a number. <a href="dart-core/num/tryParse.html">[...]</a>
                  
</dd>
      </dl>
    </section>


  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
    <ol>
    
      <li class="section-title"><a href="dart-core/num-class.html#constructors">Constructors</a></li>
      <li><a href="dart-core/num/num.html">num</a></li>
    
      <li class="section-title">
        <a href="dart-core/num-class.html#instance-properties">Properties</a>
      </li>
      <li><a href="dart-core/num/hashCode.html">hashCode</a></li>
      <li><a href="dart-core/num/isFinite.html">isFinite</a></li>
      <li><a href="dart-core/num/isInfinite.html">isInfinite</a></li>
      <li><a href="dart-core/num/isNaN.html">isNaN</a></li>
      <li><a href="dart-core/num/isNegative.html">isNegative</a></li>
      <li><a href="dart-core/num/sign.html">sign</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
      <li class="section-title"><a href="dart-core/num-class.html#instance-methods">Methods</a></li>
      <li><a href="dart-core/num/abs.html">abs</a></li>
      <li><a href="dart-core/num/ceil.html">ceil</a></li>
      <li><a href="dart-core/num/ceilToDouble.html">ceilToDouble</a></li>
      <li><a href="dart-core/num/clamp.html">clamp</a></li>
      <li><a href="dart-core/num/compareTo.html">compareTo</a></li>
      <li><a href="dart-core/num/floor.html">floor</a></li>
      <li><a href="dart-core/num/floorToDouble.html">floorToDouble</a></li>
      <li><a href="dart-core/num/remainder.html">remainder</a></li>
      <li><a href="dart-core/num/round.html">round</a></li>
      <li><a href="dart-core/num/roundToDouble.html">roundToDouble</a></li>
      <li><a href="dart-core/num/toDouble.html">toDouble</a></li>
      <li><a href="dart-core/num/toInt.html">toInt</a></li>
      <li><a href="dart-core/num/toString.html">toString</a></li>
      <li><a href="dart-core/num/toStringAsExponential.html">toStringAsExponential</a></li>
      <li><a href="dart-core/num/toStringAsFixed.html">toStringAsFixed</a></li>
      <li><a href="dart-core/num/toStringAsPrecision.html">toStringAsPrecision</a></li>
      <li><a href="dart-core/num/truncate.html">truncate</a></li>
      <li><a href="dart-core/num/truncateToDouble.html">truncateToDouble</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
    
      <li class="section-title"><a href="dart-core/num-class.html#operators">Operators</a></li>
      <li><a href="dart-core/num/operator_modulo.html">operator %</a></li>
      <li><a href="dart-core/num/operator_multiply.html">operator *</a></li>
      <li><a href="dart-core/num/operator_plus.html">operator +</a></li>
      <li><a href="dart-core/num/operator_minus.html">operator -</a></li>
      <li><a href="dart-core/num/operator_divide.html">operator /</a></li>
      <li><a href="dart-core/num/operator_less.html">operator <</a></li>
      <li><a href="dart-core/num/operator_less_equal.html">operator <=</a></li>
      <li><a href="dart-core/num/operator_equals.html">operator ==</a></li>
      <li><a href="dart-core/num/operator_greater.html">operator ></a></li>
      <li><a href="dart-core/num/operator_greater_equal.html">operator >=</a></li>
      <li><a href="dart-core/num/operator_unary_minus.html">operator unary-</a></li>
      <li><a href="dart-core/num/operator_truncate_divide.html">operator ~/</a></li>
    
    
      <li class="section-title"><a href="dart-core/num-class.html#static-methods">Static methods</a></li>
      <li><a href="dart-core/num/parse.html">parse</a></li>
      <li><a href="dart-core/num/tryParse.html">tryParse</a></li>
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
