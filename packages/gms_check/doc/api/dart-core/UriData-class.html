<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the UriData class from the dart:core library, for the Dart programming language.">
  <title>UriData class - dart:core library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
    <li class="self-crumb">UriData class</li>
  </ol>
  <div class="self-name">UriData</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
      <li class="self-crumb">UriData class</li>
    </ol>
    
    <h5>dart:core library</h5>
    <ol>
      <li class="section-title"><a href="dart-core/dart-core-library.html#classes">Classes</a></li>
      <li><a href="dart-core/BidirectionalIterator-class.html">BidirectionalIterator</a></li>
      <li><a href="dart-core/BigInt-class.html">BigInt</a></li>
      <li><a href="dart-core/bool-class.html">bool</a></li>
      <li><a href="dart-core/Comparable-class.html">Comparable</a></li>
      <li><a href="dart-core/DateTime-class.html">DateTime</a></li>
      <li><a href="dart-core/Deprecated-class.html">Deprecated</a></li>
      <li><a href="dart-core/double-class.html">double</a></li>
      <li><a href="dart-core/Duration-class.html">Duration</a></li>
      <li><a href="dart-core/Enum-class.html">Enum</a></li>
      <li><a href="dart-core/Expando-class.html">Expando</a></li>
      <li><a href="dart-core/Function-class.html">Function</a></li>
      <li><a href="dart-async/Future-class.html">Future</a></li>
      <li><a href="dart-core/int-class.html">int</a></li>
      <li><a href="dart-core/Invocation-class.html">Invocation</a></li>
      <li><a href="dart-core/Iterable-class.html">Iterable</a></li>
      <li><a href="dart-core/Iterator-class.html">Iterator</a></li>
      <li><a href="dart-core/List-class.html">List</a></li>
      <li><a href="dart-core/Map-class.html">Map</a></li>
      <li><a href="dart-core/MapEntry-class.html">MapEntry</a></li>
      <li><a href="dart-core/Match-class.html">Match</a></li>
      <li><a href="dart-core/Null-class.html">Null</a></li>
      <li><a href="dart-core/num-class.html">num</a></li>
      <li><a href="dart-core/Object-class.html">Object</a></li>
      <li><a href="dart-core/Pattern-class.html">Pattern</a></li>
      <li><a href="dart-core/pragma-class.html">pragma</a></li>
      <li><a class="deprecated" href="dart-core/Provisional-class.html">Provisional</a></li>
      <li><a href="dart-core/RegExp-class.html">RegExp</a></li>
      <li><a href="dart-core/RegExpMatch-class.html">RegExpMatch</a></li>
      <li><a href="dart-core/RuneIterator-class.html">RuneIterator</a></li>
      <li><a href="dart-core/Runes-class.html">Runes</a></li>
      <li><a href="dart-core/Set-class.html">Set</a></li>
      <li><a href="dart-core/Sink-class.html">Sink</a></li>
      <li><a href="dart-core/StackTrace-class.html">StackTrace</a></li>
      <li><a href="dart-core/Stopwatch-class.html">Stopwatch</a></li>
      <li><a href="dart-async/Stream-class.html">Stream</a></li>
      <li><a href="dart-core/String-class.html">String</a></li>
      <li><a href="dart-core/StringBuffer-class.html">StringBuffer</a></li>
      <li><a href="dart-core/StringSink-class.html">StringSink</a></li>
      <li><a href="dart-core/Symbol-class.html">Symbol</a></li>
      <li><a href="dart-core/Type-class.html">Type</a></li>
      <li><a href="dart-core/Uri-class.html">Uri</a></li>
      <li><a href="dart-core/UriData-class.html">UriData</a></li>
    
      <li class="section-title"><a href="dart-core/dart-core-library.html#extension">Extensions</a></li>
      <li><a href="dart-async/FutureExtensions.html">FutureExtensions</a></li>
    
    
      <li class="section-title"><a href="dart-core/dart-core-library.html#constants">Constants</a></li>
      <li><a href="dart-core/deprecated-constant.html">deprecated</a></li>
      <li><a href="dart-core/override-constant.html">override</a></li>
      <li><a class="deprecated" href="dart-core/provisional-constant.html">provisional</a></li>
      <li><a class="deprecated" href="dart-core/proxy-constant.html">proxy</a></li>
    
    
      <li class="section-title"><a href="dart-core/dart-core-library.html#functions">Functions</a></li>
      <li><a href="dart-core/identical.html">identical</a></li>
      <li><a href="dart-core/identityHashCode.html">identityHashCode</a></li>
      <li><a href="dart-core/print.html">print</a></li>
    
    
      <li class="section-title"><a href="dart-core/dart-core-library.html#typedefs">Typedefs</a></li>
      <li><a href="dart-core/Comparator.html">Comparator</a></li>
    
      <li class="section-title"><a href="dart-core/dart-core-library.html#exceptions">Exceptions</a></li>
      <li><a href="dart-core/AbstractClassInstantiationError-class.html">AbstractClassInstantiationError</a></li>
      <li><a href="dart-core/ArgumentError-class.html">ArgumentError</a></li>
      <li><a href="dart-core/AssertionError-class.html">AssertionError</a></li>
      <li><a class="deprecated" href="dart-core/CastError-class.html">CastError</a></li>
      <li><a href="dart-core/ConcurrentModificationError-class.html">ConcurrentModificationError</a></li>
      <li><a href="dart-core/CyclicInitializationError-class.html">CyclicInitializationError</a></li>
      <li><a href="dart-core/Error-class.html">Error</a></li>
      <li><a href="dart-core/Exception-class.html">Exception</a></li>
      <li><a href="dart-core/FallThroughError-class.html">FallThroughError</a></li>
      <li><a href="dart-core/FormatException-class.html">FormatException</a></li>
      <li><a href="dart-core/IndexError-class.html">IndexError</a></li>
      <li><a href="dart-core/IntegerDivisionByZeroException-class.html">IntegerDivisionByZeroException</a></li>
      <li><a href="dart-core/NoSuchMethodError-class.html">NoSuchMethodError</a></li>
      <li><a href="dart-core/NullThrownError-class.html">NullThrownError</a></li>
      <li><a href="dart-core/OutOfMemoryError-class.html">OutOfMemoryError</a></li>
      <li><a href="dart-core/RangeError-class.html">RangeError</a></li>
      <li><a href="dart-core/StackOverflowError-class.html">StackOverflowError</a></li>
      <li><a href="dart-core/StateError-class.html">StateError</a></li>
      <li><a href="dart-core/TypeError-class.html">TypeError</a></li>
      <li><a href="dart-core/UnimplementedError-class.html">UnimplementedError</a></li>
      <li><a href="dart-core/UnsupportedError-class.html">UnsupportedError</a></li>
    </ol>
  </div>

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-class">UriData</span> class </h1></div>

    <section class="desc markdown">
      <p>A way to access the structure of a <code>data:</code> URI.</p>
<p>Data URIs are non-hierarchical URIs that can contain any binary data.
They are defined by <a href="https://tools.ietf.org/html/rfc2397">RFC 2397</a>.</p>
<p>This class allows parsing the URI text and extracting individual parts of the
URI, as well as building the URI text from structured parts.</p>
    </section>
    

    <section class="summary offset-anchor" id="constructors">
      <h2>Constructors</h2>

      <dl class="constructor-summary-list">
        <dt id="UriData.fromBytes" class="callable">
          <span class="name"><a href="dart-core/UriData/UriData.fromBytes.html">UriData.fromBytes</a></span><span class="signature">(<span class="parameter" id="fromBytes-param-bytes"><span class="type-annotation"><a href="dart-core/List-class.html">List</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-core/int-class.html">int</a></span>&gt;</span></span> <span class="parameter-name">bytes</span>, {</span> <span class="parameter" id="fromBytes-param-mimeType"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">mimeType</span>: <span class="default-value">"application/octet-stream"</span>, </span> <span class="parameter" id="fromBytes-param-parameters"><span class="type-annotation"><a href="dart-core/Map-class.html">Map</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-core/String-class.html">String</a></span>, <span class="type-parameter"><a href="dart-core/String-class.html">String</a></span>&gt;</span></span> <span class="parameter-name">parameters</span>, </span> <span class="parameter" id="fromBytes-param-percentEncoded"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">percentEncoded</span>: <span class="default-value">false</span></span> })</span>
        </dt>
        <dd>
          Creates a <code>data:</code> URI containing an encoding of <code>bytes</code>. <a href="dart-core/UriData/UriData.fromBytes.html">[...]</a>
          <div class="constructor-modifier features">factory</div>
        </dd>
        <dt id="UriData.fromString" class="callable">
          <span class="name"><a href="dart-core/UriData/UriData.fromString.html">UriData.fromString</a></span><span class="signature">(<span class="parameter" id="fromString-param-content"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">content</span>, {</span> <span class="parameter" id="fromString-param-mimeType"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">mimeType</span>, </span> <span class="parameter" id="fromString-param-encoding"><span class="type-annotation"><a href="dart-convert/Encoding-class.html">Encoding</a></span> <span class="parameter-name">encoding</span>, </span> <span class="parameter" id="fromString-param-parameters"><span class="type-annotation"><a href="dart-core/Map-class.html">Map</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-core/String-class.html">String</a></span>, <span class="type-parameter"><a href="dart-core/String-class.html">String</a></span>&gt;</span></span> <span class="parameter-name">parameters</span>, </span> <span class="parameter" id="fromString-param-base64"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">base64</span>: <span class="default-value">false</span></span> })</span>
        </dt>
        <dd>
          Creates a <code>data:</code> URI containing the <code>content</code> string. <a href="dart-core/UriData/UriData.fromString.html">[...]</a>
          <div class="constructor-modifier features">factory</div>
        </dd>
        <dt id="UriData.fromUri" class="callable">
          <span class="name"><a href="dart-core/UriData/UriData.fromUri.html">UriData.fromUri</a></span><span class="signature">(<span class="parameter" id="fromUri-param-uri"><span class="type-annotation"><a href="dart-core/Uri-class.html">Uri</a></span> <span class="parameter-name">uri</span></span>)</span>
        </dt>
        <dd>
          Creates a <code>DataUri</code> from a <a href="dart-core/Uri-class.html">Uri</a> which must have <code>data</code> as <a href="dart-core/Uri/scheme.html">Uri.scheme</a>. <a href="dart-core/UriData/UriData.fromUri.html">[...]</a>
          <div class="constructor-modifier features">factory</div>
        </dd>
      </dl>
    </section>

    <section class="summary offset-anchor" id="instance-properties">
      <h2>Properties</h2>

      <dl class="properties">
        <dt id="charset" class="property">
          <span class="name"><a href="dart-core/UriData/charset.html">charset</a></span>
          <span class="signature">&#8594; <a href="dart-core/String-class.html">String</a></span>         
        </dt>
        <dd>
          The charset parameter of the media type. <a href="dart-core/UriData/charset.html">[...]</a>
                  <div class="features">read-only</div>
</dd>
        <dt id="contentText" class="property">
          <span class="name"><a href="dart-core/UriData/contentText.html">contentText</a></span>
          <span class="signature">&#8594; <a href="dart-core/String-class.html">String</a></span>         
        </dt>
        <dd>
          The content part of the data URI, as its actual representation. <a href="dart-core/UriData/contentText.html">[...]</a>
                  <div class="features">read-only</div>
</dd>
        <dt id="isBase64" class="property">
          <span class="name"><a href="dart-core/UriData/isBase64.html">isBase64</a></span>
          <span class="signature">&#8594; <a href="dart-core/bool-class.html">bool</a></span>         
        </dt>
        <dd>
          Whether the data is Base64 encoded or not.
                  <div class="features">read-only</div>
</dd>
        <dt id="mimeType" class="property">
          <span class="name"><a href="dart-core/UriData/mimeType.html">mimeType</a></span>
          <span class="signature">&#8594; <a href="dart-core/String-class.html">String</a></span>         
        </dt>
        <dd>
          The MIME type of the data URI. <a href="dart-core/UriData/mimeType.html">[...]</a>
                  <div class="features">read-only</div>
</dd>
        <dt id="parameters" class="property">
          <span class="name"><a href="dart-core/UriData/parameters.html">parameters</a></span>
          <span class="signature">&#8594; <a href="dart-core/Map-class.html">Map</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-core/String-class.html">String</a></span>, <span class="type-parameter"><a href="dart-core/String-class.html">String</a></span>&gt;</span></span>         
        </dt>
        <dd>
          A map representing the parameters of the media type. <a href="dart-core/UriData/parameters.html">[...]</a>
                  <div class="features">read-only</div>
</dd>
        <dt id="uri" class="property">
          <span class="name"><a href="dart-core/UriData/uri.html">uri</a></span>
          <span class="signature">&#8594; <a href="dart-core/Uri-class.html">Uri</a></span>         
        </dt>
        <dd>
          The <a href="dart-core/Uri-class.html">Uri</a> that this <code>UriData</code> is giving access to. <a href="dart-core/UriData/uri.html">[...]</a>
                  <div class="features">read-only</div>
</dd>
        <dt id="hashCode" class="property inherited">
          <span class="name"><a href="dart-core/Object/hashCode.html">hashCode</a></span>
          <span class="signature">&#8594; <a href="dart-core/int-class.html">int</a></span>         
        </dt>
        <dd class="inherited">
          The hash code for this object. <a href="dart-core/Object/hashCode.html">[...]</a>
                  <div class="features">read-only, inherited</div>
</dd>
        <dt id="runtimeType" class="property inherited">
          <span class="name"><a href="dart-core/Object/runtimeType.html">runtimeType</a></span>
          <span class="signature">&#8594; <a href="dart-core/Type-class.html">Type</a></span>         
        </dt>
        <dd class="inherited">
          A representation of the runtime type of the object.
                  <div class="features">read-only, inherited</div>
</dd>
      </dl>
    </section>

    <section class="summary offset-anchor" id="instance-methods">
      <h2>Methods</h2>
      <dl class="callables">
        <dt id="contentAsBytes" class="callable">
          <span class="name"><a href="dart-core/UriData/contentAsBytes.html">contentAsBytes</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-typed_data/Uint8List-class.html">Uint8List</a></span>
          </span>
                  </dt>
        <dd>
          The content part of the data URI as bytes. <a href="dart-core/UriData/contentAsBytes.html">[...]</a>
                  
</dd>
        <dt id="contentAsString" class="callable">
          <span class="name"><a href="dart-core/UriData/contentAsString.html">contentAsString</a></span><span class="signature">(<wbr>{<span class="parameter" id="contentAsString-param-encoding"><span class="type-annotation"><a href="dart-convert/Encoding-class.html">Encoding</a></span> <span class="parameter-name">encoding</span></span> })
            <span class="returntype parameter">&#8594; <a href="dart-core/String-class.html">String</a></span>
          </span>
                  </dt>
        <dd>
          Creates a string from the content of the data URI. <a href="dart-core/UriData/contentAsString.html">[...]</a>
                  
</dd>
        <dt id="toString" class="callable">
          <span class="name"><a href="dart-core/UriData/toString.html">toString</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-core/String-class.html">String</a></span>
          </span>
                  </dt>
        <dd>
          A string representation of this object. <a href="dart-core/UriData/toString.html">[...]</a>
                  <div class="features">override</div>
</dd>
        <dt id="noSuchMethod" class="callable inherited">
          <span class="name"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></span><span class="signature">(<wbr><span class="parameter" id="noSuchMethod-param-invocation"><span class="type-annotation"><a href="dart-core/Invocation-class.html">Invocation</a></span> <span class="parameter-name">invocation</span></span>)
            <span class="returntype parameter">&#8594; dynamic</span>
          </span>
                  </dt>
        <dd class="inherited">
          Invoked when a non-existent method or property is accessed. <a href="dart-core/Object/noSuchMethod.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
      </dl>
    </section>

    <section class="summary offset-anchor inherited" id="operators">
      <h2>Operators</h2>
      <dl class="callables">
        <dt id="operator ==" class="callable inherited">
          <span class="name"><a href="dart-core/Object/operator_equals.html">operator ==</a></span><span class="signature">(<wbr><span class="parameter" id="==-param-other"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">other</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/bool-class.html">bool</a></span>
          </span>
                  </dt>
        <dd class="inherited">
          The equality operator. <a href="dart-core/Object/operator_equals.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
      </dl>
    </section>


    <section class="summary offset-anchor" id="static-methods">
      <h2>Static Methods</h2>
      <dl class="callables">
        <dt id="parse" class="callable">
          <span class="name"><a href="dart-core/UriData/parse.html">parse</a></span><span class="signature">(<wbr><span class="parameter" id="parse-param-uri"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">uri</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/UriData-class.html">UriData</a></span>
          </span>
                  </dt>
        <dd>
          Parses a string as a <code>data</code> URI. <a href="dart-core/UriData/parse.html">[...]</a>
                  
</dd>
      </dl>
    </section>


  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
    <ol>
    
      <li class="section-title"><a href="dart-core/UriData-class.html#constructors">Constructors</a></li>
      <li><a href="dart-core/UriData/UriData.fromBytes.html">fromBytes</a></li>
      <li><a href="dart-core/UriData/UriData.fromString.html">fromString</a></li>
      <li><a href="dart-core/UriData/UriData.fromUri.html">fromUri</a></li>
    
      <li class="section-title">
        <a href="dart-core/UriData-class.html#instance-properties">Properties</a>
      </li>
      <li><a href="dart-core/UriData/charset.html">charset</a></li>
      <li><a href="dart-core/UriData/contentText.html">contentText</a></li>
      <li><a href="dart-core/UriData/isBase64.html">isBase64</a></li>
      <li><a href="dart-core/UriData/mimeType.html">mimeType</a></li>
      <li><a href="dart-core/UriData/parameters.html">parameters</a></li>
      <li><a href="dart-core/UriData/uri.html">uri</a></li>
      <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
      <li class="section-title"><a href="dart-core/UriData-class.html#instance-methods">Methods</a></li>
      <li><a href="dart-core/UriData/contentAsBytes.html">contentAsBytes</a></li>
      <li><a href="dart-core/UriData/contentAsString.html">contentAsString</a></li>
      <li><a href="dart-core/UriData/toString.html">toString</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
    
      <li class="section-title inherited"><a href="dart-core/UriData-class.html#operators">Operators</a></li>
      <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
      <li class="section-title"><a href="dart-core/UriData-class.html#static-methods">Static methods</a></li>
      <li><a href="dart-core/UriData/parse.html">parse</a></li>
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
