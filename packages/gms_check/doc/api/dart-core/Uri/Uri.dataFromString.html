<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the Uri.dataFromString constructor from the Class Uri class from the dart:core library, for the Dart programming language.">
  <title>Uri.dataFromString constructor - Uri class - dart:core library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
    <li><a href="dart-core/Uri-class.html">Uri</a></li>
    <li class="self-crumb">Uri.dataFromString factory constructor</li>
  </ol>
  <div class="self-name">Uri.dataFromString</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
      <li><a href="dart-core/Uri-class.html">Uri</a></li>
      <li class="self-crumb">Uri.dataFromString factory constructor</li>
    </ol>
    
    <h5>Uri class</h5>
    <ol>
    
      <li class="section-title"><a href="dart-core/Uri-class.html#constructors">Constructors</a></li>
      <li><a href="dart-core/Uri/Uri.html">Uri</a></li>
      <li><a href="dart-core/Uri/Uri.dataFromBytes.html">dataFromBytes</a></li>
      <li><a href="dart-core/Uri/Uri.dataFromString.html">dataFromString</a></li>
      <li><a href="dart-core/Uri/Uri.directory.html">directory</a></li>
      <li><a href="dart-core/Uri/Uri.file.html">file</a></li>
      <li><a href="dart-core/Uri/Uri.http.html">http</a></li>
      <li><a href="dart-core/Uri/Uri.https.html">https</a></li>
    
      <li class="section-title">
        <a href="dart-core/Uri-class.html#instance-properties">Properties</a>
      </li>
      <li><a href="dart-core/Uri/authority.html">authority</a></li>
      <li><a href="dart-core/Uri/data.html">data</a></li>
      <li><a href="dart-core/Uri/fragment.html">fragment</a></li>
      <li><a href="dart-core/Uri/hasAbsolutePath.html">hasAbsolutePath</a></li>
      <li><a href="dart-core/Uri/hasAuthority.html">hasAuthority</a></li>
      <li><a href="dart-core/Uri/hasEmptyPath.html">hasEmptyPath</a></li>
      <li><a href="dart-core/Uri/hasFragment.html">hasFragment</a></li>
      <li><a href="dart-core/Uri/hashCode.html">hashCode</a></li>
      <li><a href="dart-core/Uri/hasPort.html">hasPort</a></li>
      <li><a href="dart-core/Uri/hasQuery.html">hasQuery</a></li>
      <li><a href="dart-core/Uri/hasScheme.html">hasScheme</a></li>
      <li><a href="dart-core/Uri/host.html">host</a></li>
      <li><a href="dart-core/Uri/isAbsolute.html">isAbsolute</a></li>
      <li><a href="dart-core/Uri/origin.html">origin</a></li>
      <li><a href="dart-core/Uri/path.html">path</a></li>
      <li><a href="dart-core/Uri/pathSegments.html">pathSegments</a></li>
      <li><a href="dart-core/Uri/port.html">port</a></li>
      <li><a href="dart-core/Uri/query.html">query</a></li>
      <li><a href="dart-core/Uri/queryParameters.html">queryParameters</a></li>
      <li><a href="dart-core/Uri/queryParametersAll.html">queryParametersAll</a></li>
      <li><a href="dart-core/Uri/scheme.html">scheme</a></li>
      <li><a href="dart-core/Uri/userInfo.html">userInfo</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
      <li class="section-title"><a href="dart-core/Uri-class.html#instance-methods">Methods</a></li>
      <li><a href="dart-core/Uri/isScheme.html">isScheme</a></li>
      <li><a href="dart-core/Uri/normalizePath.html">normalizePath</a></li>
      <li><a href="dart-core/Uri/removeFragment.html">removeFragment</a></li>
      <li><a href="dart-core/Uri/replace.html">replace</a></li>
      <li><a href="dart-core/Uri/resolve.html">resolve</a></li>
      <li><a href="dart-core/Uri/resolveUri.html">resolveUri</a></li>
      <li><a href="dart-core/Uri/toFilePath.html">toFilePath</a></li>
      <li><a href="dart-core/Uri/toString.html">toString</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
    
      <li class="section-title"><a href="dart-core/Uri-class.html#operators">Operators</a></li>
      <li><a href="dart-core/Uri/operator_equals.html">operator ==</a></li>
    
      <li class="section-title"><a href="dart-core/Uri-class.html#static-properties">Static properties</a></li>
      <li><a href="dart-core/Uri/base.html">base</a></li>
    
      <li class="section-title"><a href="dart-core/Uri-class.html#static-methods">Static methods</a></li>
      <li><a href="dart-core/Uri/decodeComponent.html">decodeComponent</a></li>
      <li><a href="dart-core/Uri/decodeFull.html">decodeFull</a></li>
      <li><a href="dart-core/Uri/decodeQueryComponent.html">decodeQueryComponent</a></li>
      <li><a href="dart-core/Uri/encodeComponent.html">encodeComponent</a></li>
      <li><a href="dart-core/Uri/encodeFull.html">encodeFull</a></li>
      <li><a href="dart-core/Uri/encodeQueryComponent.html">encodeQueryComponent</a></li>
      <li><a href="dart-core/Uri/parse.html">parse</a></li>
      <li><a href="dart-core/Uri/parseIPv4Address.html">parseIPv4Address</a></li>
      <li><a href="dart-core/Uri/parseIPv6Address.html">parseIPv6Address</a></li>
      <li><a href="dart-core/Uri/splitQueryString.html">splitQueryString</a></li>
      <li><a href="dart-core/Uri/tryParse.html">tryParse</a></li>
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-left-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-constructor">Uri.dataFromString</span> constructor</h1></div>

    <section class="multi-line-signature">
      
      <span class="name ">Uri.dataFromString</span>(<wbr><span class="parameter" id="dataFromString-param-content"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">content</span>, {</span> <span class="parameter" id="dataFromString-param-mimeType"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">mimeType</span>, </span> <span class="parameter" id="dataFromString-param-encoding"><span class="type-annotation"><a href="dart-convert/Encoding-class.html">Encoding</a></span> <span class="parameter-name">encoding</span>, </span> <span class="parameter" id="dataFromString-param-parameters"><span class="type-annotation"><a href="dart-core/Map-class.html">Map</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-core/String-class.html">String</a></span>, <span class="type-parameter"><a href="dart-core/String-class.html">String</a></span>&gt;</span></span> <span class="parameter-name">parameters</span>, </span> <span class="parameter" id="dataFromString-param-base64"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">base64</span>: <span class="default-value">false</span></span> })
    </section>

    <section class="desc markdown">
      <p>Creates a <code>data:</code> URI containing the <code>content</code> string.</p>
<p>Converts the content to a bytes using <code>encoding</code> or the charset specified
in <code>parameters</code> (defaulting to US-ASCII if not specified or unrecognized),
then encodes the bytes into the resulting data URI.</p>
<p>Defaults to encoding using percent-encoding (any non-ASCII or non-URI-valid
bytes is replaced by a percent encoding). If <code>base64</code> is true, the bytes
are instead encoded using <code>base64</code>.</p>
<p>If <code>encoding</code> is not provided and <code>parameters</code> has a <code>charset</code> entry,
that name is looked up using <a href="dart-convert/Encoding/getByName.html">Encoding.getByName</a>,
and if the lookup returns an encoding, that encoding is used to convert
<code>content</code> to bytes.
If providing both an <code>encoding</code> and a charset in <code>parameters</code>, they should
agree, otherwise decoding won't be able to use the charset parameter
to determine the encoding.</p>
<p>If <code>mimeType</code> and/or <code>parameters</code> are supplied, they are added to the
created URI. If any of these contain characters that are not allowed
in the data URI, the character is percent-escaped. If the character is
non-ASCII, it is first UTF-8 encoded and then the bytes are percent
encoded. An omitted <code>mimeType</code> in a data URI means <code>text/plain</code>, just
as an omitted <code>charset</code> parameter defaults to meaning <code>US-ASCII</code>.</p>
<p>To read the content back, use <a href="dart-core/UriData/contentAsString.html">UriData.contentAsString</a>.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">factory Uri.dataFromString(String content,
    {String? mimeType,
    Encoding? encoding,
    Map&lt;String, String&gt;? parameters,
    bool base64 = false}) {
  UriData data = UriData.fromString(content,
      mimeType: mimeType,
      encoding: encoding,
      parameters: parameters,
      base64: base64);
  return data.uri;
}</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
