<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the Invocation class from the dart:core library, for the Dart programming language.">
  <title>Invocation class - dart:core library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
    <li class="self-crumb">Invocation abstract class</li>
  </ol>
  <div class="self-name">Invocation</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
      <li class="self-crumb">Invocation abstract class</li>
    </ol>
    
    <h5>dart:core library</h5>
    <ol>
      <li class="section-title"><a href="dart-core/dart-core-library.html#classes">Classes</a></li>
      <li><a href="dart-core/BidirectionalIterator-class.html">BidirectionalIterator</a></li>
      <li><a href="dart-core/BigInt-class.html">BigInt</a></li>
      <li><a href="dart-core/bool-class.html">bool</a></li>
      <li><a href="dart-core/Comparable-class.html">Comparable</a></li>
      <li><a href="dart-core/DateTime-class.html">DateTime</a></li>
      <li><a href="dart-core/Deprecated-class.html">Deprecated</a></li>
      <li><a href="dart-core/double-class.html">double</a></li>
      <li><a href="dart-core/Duration-class.html">Duration</a></li>
      <li><a href="dart-core/Enum-class.html">Enum</a></li>
      <li><a href="dart-core/Expando-class.html">Expando</a></li>
      <li><a href="dart-core/Function-class.html">Function</a></li>
      <li><a href="dart-async/Future-class.html">Future</a></li>
      <li><a href="dart-core/int-class.html">int</a></li>
      <li><a href="dart-core/Invocation-class.html">Invocation</a></li>
      <li><a href="dart-core/Iterable-class.html">Iterable</a></li>
      <li><a href="dart-core/Iterator-class.html">Iterator</a></li>
      <li><a href="dart-core/List-class.html">List</a></li>
      <li><a href="dart-core/Map-class.html">Map</a></li>
      <li><a href="dart-core/MapEntry-class.html">MapEntry</a></li>
      <li><a href="dart-core/Match-class.html">Match</a></li>
      <li><a href="dart-core/Null-class.html">Null</a></li>
      <li><a href="dart-core/num-class.html">num</a></li>
      <li><a href="dart-core/Object-class.html">Object</a></li>
      <li><a href="dart-core/Pattern-class.html">Pattern</a></li>
      <li><a href="dart-core/pragma-class.html">pragma</a></li>
      <li><a class="deprecated" href="dart-core/Provisional-class.html">Provisional</a></li>
      <li><a href="dart-core/RegExp-class.html">RegExp</a></li>
      <li><a href="dart-core/RegExpMatch-class.html">RegExpMatch</a></li>
      <li><a href="dart-core/RuneIterator-class.html">RuneIterator</a></li>
      <li><a href="dart-core/Runes-class.html">Runes</a></li>
      <li><a href="dart-core/Set-class.html">Set</a></li>
      <li><a href="dart-core/Sink-class.html">Sink</a></li>
      <li><a href="dart-core/StackTrace-class.html">StackTrace</a></li>
      <li><a href="dart-core/Stopwatch-class.html">Stopwatch</a></li>
      <li><a href="dart-async/Stream-class.html">Stream</a></li>
      <li><a href="dart-core/String-class.html">String</a></li>
      <li><a href="dart-core/StringBuffer-class.html">StringBuffer</a></li>
      <li><a href="dart-core/StringSink-class.html">StringSink</a></li>
      <li><a href="dart-core/Symbol-class.html">Symbol</a></li>
      <li><a href="dart-core/Type-class.html">Type</a></li>
      <li><a href="dart-core/Uri-class.html">Uri</a></li>
      <li><a href="dart-core/UriData-class.html">UriData</a></li>
    
      <li class="section-title"><a href="dart-core/dart-core-library.html#extension">Extensions</a></li>
      <li><a href="dart-async/FutureExtensions.html">FutureExtensions</a></li>
    
    
      <li class="section-title"><a href="dart-core/dart-core-library.html#constants">Constants</a></li>
      <li><a href="dart-core/deprecated-constant.html">deprecated</a></li>
      <li><a href="dart-core/override-constant.html">override</a></li>
      <li><a class="deprecated" href="dart-core/provisional-constant.html">provisional</a></li>
      <li><a class="deprecated" href="dart-core/proxy-constant.html">proxy</a></li>
    
    
      <li class="section-title"><a href="dart-core/dart-core-library.html#functions">Functions</a></li>
      <li><a href="dart-core/identical.html">identical</a></li>
      <li><a href="dart-core/identityHashCode.html">identityHashCode</a></li>
      <li><a href="dart-core/print.html">print</a></li>
    
    
      <li class="section-title"><a href="dart-core/dart-core-library.html#typedefs">Typedefs</a></li>
      <li><a href="dart-core/Comparator.html">Comparator</a></li>
    
      <li class="section-title"><a href="dart-core/dart-core-library.html#exceptions">Exceptions</a></li>
      <li><a href="dart-core/AbstractClassInstantiationError-class.html">AbstractClassInstantiationError</a></li>
      <li><a href="dart-core/ArgumentError-class.html">ArgumentError</a></li>
      <li><a href="dart-core/AssertionError-class.html">AssertionError</a></li>
      <li><a class="deprecated" href="dart-core/CastError-class.html">CastError</a></li>
      <li><a href="dart-core/ConcurrentModificationError-class.html">ConcurrentModificationError</a></li>
      <li><a href="dart-core/CyclicInitializationError-class.html">CyclicInitializationError</a></li>
      <li><a href="dart-core/Error-class.html">Error</a></li>
      <li><a href="dart-core/Exception-class.html">Exception</a></li>
      <li><a href="dart-core/FallThroughError-class.html">FallThroughError</a></li>
      <li><a href="dart-core/FormatException-class.html">FormatException</a></li>
      <li><a href="dart-core/IndexError-class.html">IndexError</a></li>
      <li><a href="dart-core/IntegerDivisionByZeroException-class.html">IntegerDivisionByZeroException</a></li>
      <li><a href="dart-core/NoSuchMethodError-class.html">NoSuchMethodError</a></li>
      <li><a href="dart-core/NullThrownError-class.html">NullThrownError</a></li>
      <li><a href="dart-core/OutOfMemoryError-class.html">OutOfMemoryError</a></li>
      <li><a href="dart-core/RangeError-class.html">RangeError</a></li>
      <li><a href="dart-core/StackOverflowError-class.html">StackOverflowError</a></li>
      <li><a href="dart-core/StateError-class.html">StateError</a></li>
      <li><a href="dart-core/TypeError-class.html">TypeError</a></li>
      <li><a href="dart-core/UnimplementedError-class.html">UnimplementedError</a></li>
      <li><a href="dart-core/UnsupportedError-class.html">UnsupportedError</a></li>
    </ol>
  </div>

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-class">Invocation</span> class </h1></div>

    <section class="desc markdown">
      <p>Representation of the invocation of a member on an object.</p>
<p>This is the type of objects passed to <a href="dart-core/Object/noSuchMethod.html">Object.noSuchMethod</a> when
an object doesn't support the member invocation that was attempted
on it.</p>
    </section>
    

    <section class="summary offset-anchor" id="constructors">
      <h2>Constructors</h2>

      <dl class="constructor-summary-list">
        <dt id="Invocation" class="callable">
          <span class="name"><a href="dart-core/Invocation/Invocation.html">Invocation</a></span><span class="signature">()</span>
        </dt>
        <dd>
          
        </dd>
        <dt id="Invocation.genericMethod" class="callable">
          <span class="name"><a href="dart-core/Invocation/Invocation.genericMethod.html">Invocation.genericMethod</a></span><span class="signature">(<span class="parameter" id="genericMethod-param-memberName"><span class="type-annotation"><a href="dart-core/Symbol-class.html">Symbol</a></span> <span class="parameter-name">memberName</span>, </span> <span class="parameter" id="genericMethod-param-typeArguments"><span class="type-annotation"><a href="dart-core/Iterable-class.html">Iterable</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-core/Type-class.html">Type</a></span>&gt;</span></span> <span class="parameter-name">typeArguments</span>, </span> <span class="parameter" id="genericMethod-param-positionalArguments"><span class="type-annotation"><a href="dart-core/Iterable-class.html">Iterable</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-core/Object-class.html">Object</a></span>&gt;</span></span> <span class="parameter-name">positionalArguments</span>, [</span> <span class="parameter" id="genericMethod-param-namedArguments"><span class="type-annotation"><a href="dart-core/Map-class.html">Map</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-core/Symbol-class.html">Symbol</a></span>, <span class="type-parameter"><a href="dart-core/Object-class.html">Object</a></span>&gt;</span></span> <span class="parameter-name">namedArguments</span></span> ])</span>
        </dt>
        <dd>
          Creates an invocation corresponding to a generic method invocation. <a href="dart-core/Invocation/Invocation.genericMethod.html">[...]</a>
          <div class="constructor-modifier features">factory</div>
        </dd>
        <dt id="Invocation.getter" class="callable">
          <span class="name"><a href="dart-core/Invocation/Invocation.getter.html">Invocation.getter</a></span><span class="signature">(<span class="parameter" id="getter-param-name"><span class="type-annotation"><a href="dart-core/Symbol-class.html">Symbol</a></span> <span class="parameter-name">name</span></span>)</span>
        </dt>
        <dd>
          Creates an invocation corresponding to a getter invocation.
          <div class="constructor-modifier features">factory</div>
        </dd>
        <dt id="Invocation.method" class="callable">
          <span class="name"><a href="dart-core/Invocation/Invocation.method.html">Invocation.method</a></span><span class="signature">(<span class="parameter" id="method-param-memberName"><span class="type-annotation"><a href="dart-core/Symbol-class.html">Symbol</a></span> <span class="parameter-name">memberName</span>, </span> <span class="parameter" id="method-param-positionalArguments"><span class="type-annotation"><a href="dart-core/Iterable-class.html">Iterable</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-core/Object-class.html">Object</a></span>&gt;</span></span> <span class="parameter-name">positionalArguments</span>, [</span> <span class="parameter" id="method-param-namedArguments"><span class="type-annotation"><a href="dart-core/Map-class.html">Map</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-core/Symbol-class.html">Symbol</a></span>, <span class="type-parameter"><a href="dart-core/Object-class.html">Object</a></span>&gt;</span></span> <span class="parameter-name">namedArguments</span></span> ])</span>
        </dt>
        <dd>
          Creates an invocation corresponding to a method invocation. <a href="dart-core/Invocation/Invocation.method.html">[...]</a>
          <div class="constructor-modifier features">factory</div>
        </dd>
        <dt id="Invocation.setter" class="callable">
          <span class="name"><a href="dart-core/Invocation/Invocation.setter.html">Invocation.setter</a></span><span class="signature">(<span class="parameter" id="setter-param-memberName"><span class="type-annotation"><a href="dart-core/Symbol-class.html">Symbol</a></span> <span class="parameter-name">memberName</span>, </span> <span class="parameter" id="setter-param-argument"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">argument</span></span>)</span>
        </dt>
        <dd>
          Creates an invocation corresponding to a setter invocation. <a href="dart-core/Invocation/Invocation.setter.html">[...]</a>
          <div class="constructor-modifier features">factory</div>
        </dd>
      </dl>
    </section>

    <section class="summary offset-anchor" id="instance-properties">
      <h2>Properties</h2>

      <dl class="properties">
        <dt id="isAccessor" class="property">
          <span class="name"><a href="dart-core/Invocation/isAccessor.html">isAccessor</a></span>
          <span class="signature">&#8594; <a href="dart-core/bool-class.html">bool</a></span>         
        </dt>
        <dd>
          Whether the invocation was a getter or a setter call.
                  <div class="features">read-only</div>
</dd>
        <dt id="isGetter" class="property">
          <span class="name"><a href="dart-core/Invocation/isGetter.html">isGetter</a></span>
          <span class="signature">&#8594; <a href="dart-core/bool-class.html">bool</a></span>         
        </dt>
        <dd>
          Whether the invocation was a getter call.
If so, all three types of arguments lists are empty.
                  <div class="features">read-only</div>
</dd>
        <dt id="isMethod" class="property">
          <span class="name"><a href="dart-core/Invocation/isMethod.html">isMethod</a></span>
          <span class="signature">&#8594; <a href="dart-core/bool-class.html">bool</a></span>         
        </dt>
        <dd>
          Whether the invocation was a method call.
                  <div class="features">read-only</div>
</dd>
        <dt id="isSetter" class="property">
          <span class="name"><a href="dart-core/Invocation/isSetter.html">isSetter</a></span>
          <span class="signature">&#8594; <a href="dart-core/bool-class.html">bool</a></span>         
        </dt>
        <dd>
          Whether the invocation was a setter call. <a href="dart-core/Invocation/isSetter.html">[...]</a>
                  <div class="features">read-only</div>
</dd>
        <dt id="memberName" class="property">
          <span class="name"><a href="dart-core/Invocation/memberName.html">memberName</a></span>
          <span class="signature">&#8594; <a href="dart-core/Symbol-class.html">Symbol</a></span>         
        </dt>
        <dd>
          The name of the invoked member.
                  <div class="features">read-only</div>
</dd>
        <dt id="namedArguments" class="property">
          <span class="name"><a href="dart-core/Invocation/namedArguments.html">namedArguments</a></span>
          <span class="signature">&#8594; <a href="dart-core/Map-class.html">Map</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-core/Symbol-class.html">Symbol</a></span>, <span class="type-parameter">dynamic</span>&gt;</span></span>         
        </dt>
        <dd>
          An unmodifiable view of the named arguments of the call. <a href="dart-core/Invocation/namedArguments.html">[...]</a>
                  <div class="features">read-only</div>
</dd>
        <dt id="positionalArguments" class="property">
          <span class="name"><a href="dart-core/Invocation/positionalArguments.html">positionalArguments</a></span>
          <span class="signature">&#8594; <a href="dart-core/List-class.html">List</a></span>         
        </dt>
        <dd>
          An unmodifiable view of the positional arguments of the call. <a href="dart-core/Invocation/positionalArguments.html">[...]</a>
                  <div class="features">read-only</div>
</dd>
        <dt id="typeArguments" class="property">
          <span class="name"><a href="dart-core/Invocation/typeArguments.html">typeArguments</a></span>
          <span class="signature">&#8594; <a href="dart-core/List-class.html">List</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-core/Type-class.html">Type</a></span>&gt;</span></span>         
        </dt>
        <dd>
          An unmodifiable view of the type arguments of the call. <a href="dart-core/Invocation/typeArguments.html">[...]</a>
                  <div class="features">read-only</div>
</dd>
        <dt id="hashCode" class="property inherited">
          <span class="name"><a href="dart-core/Object/hashCode.html">hashCode</a></span>
          <span class="signature">&#8594; <a href="dart-core/int-class.html">int</a></span>         
        </dt>
        <dd class="inherited">
          The hash code for this object. <a href="dart-core/Object/hashCode.html">[...]</a>
                  <div class="features">read-only, inherited</div>
</dd>
        <dt id="runtimeType" class="property inherited">
          <span class="name"><a href="dart-core/Object/runtimeType.html">runtimeType</a></span>
          <span class="signature">&#8594; <a href="dart-core/Type-class.html">Type</a></span>         
        </dt>
        <dd class="inherited">
          A representation of the runtime type of the object.
                  <div class="features">read-only, inherited</div>
</dd>
      </dl>
    </section>

    <section class="summary offset-anchor inherited" id="instance-methods">
      <h2>Methods</h2>
      <dl class="callables">
        <dt id="noSuchMethod" class="callable inherited">
          <span class="name"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></span><span class="signature">(<wbr><span class="parameter" id="noSuchMethod-param-invocation"><span class="type-annotation"><a href="dart-core/Invocation-class.html">Invocation</a></span> <span class="parameter-name">invocation</span></span>)
            <span class="returntype parameter">&#8594; dynamic</span>
          </span>
                  </dt>
        <dd class="inherited">
          Invoked when a non-existent method or property is accessed. <a href="dart-core/Object/noSuchMethod.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="toString" class="callable inherited">
          <span class="name"><a href="dart-core/Object/toString.html">toString</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-core/String-class.html">String</a></span>
          </span>
                  </dt>
        <dd class="inherited">
          A string representation of this object. <a href="dart-core/Object/toString.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
      </dl>
    </section>

    <section class="summary offset-anchor inherited" id="operators">
      <h2>Operators</h2>
      <dl class="callables">
        <dt id="operator ==" class="callable inherited">
          <span class="name"><a href="dart-core/Object/operator_equals.html">operator ==</a></span><span class="signature">(<wbr><span class="parameter" id="==-param-other"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">other</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/bool-class.html">bool</a></span>
          </span>
                  </dt>
        <dd class="inherited">
          The equality operator. <a href="dart-core/Object/operator_equals.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
      </dl>
    </section>




  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
    <ol>
    
      <li class="section-title"><a href="dart-core/Invocation-class.html#constructors">Constructors</a></li>
      <li><a href="dart-core/Invocation/Invocation.html">Invocation</a></li>
      <li><a href="dart-core/Invocation/Invocation.genericMethod.html">genericMethod</a></li>
      <li><a href="dart-core/Invocation/Invocation.getter.html">getter</a></li>
      <li><a href="dart-core/Invocation/Invocation.method.html">method</a></li>
      <li><a href="dart-core/Invocation/Invocation.setter.html">setter</a></li>
    
      <li class="section-title">
        <a href="dart-core/Invocation-class.html#instance-properties">Properties</a>
      </li>
      <li><a href="dart-core/Invocation/isAccessor.html">isAccessor</a></li>
      <li><a href="dart-core/Invocation/isGetter.html">isGetter</a></li>
      <li><a href="dart-core/Invocation/isMethod.html">isMethod</a></li>
      <li><a href="dart-core/Invocation/isSetter.html">isSetter</a></li>
      <li><a href="dart-core/Invocation/memberName.html">memberName</a></li>
      <li><a href="dart-core/Invocation/namedArguments.html">namedArguments</a></li>
      <li><a href="dart-core/Invocation/positionalArguments.html">positionalArguments</a></li>
      <li><a href="dart-core/Invocation/typeArguments.html">typeArguments</a></li>
      <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
      <li class="section-title inherited"><a href="dart-core/Invocation-class.html#instance-methods">Methods</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
      <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
    
      <li class="section-title inherited"><a href="dart-core/Invocation-class.html#operators">Operators</a></li>
      <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
