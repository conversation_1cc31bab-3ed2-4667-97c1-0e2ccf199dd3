<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the IndexError constructor from the Class IndexError class from the dart:core library, for the Dart programming language.">
  <title>IndexError constructor - IndexError class - dart:core library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
    <li><a href="dart-core/IndexError-class.html">IndexError</a></li>
    <li class="self-crumb">IndexError constructor</li>
  </ol>
  <div class="self-name">IndexError</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
      <li><a href="dart-core/IndexError-class.html">IndexError</a></li>
      <li class="self-crumb">IndexError constructor</li>
    </ol>
    
    <h5>IndexError class</h5>
    <ol>
    
      <li class="section-title"><a href="dart-core/IndexError-class.html#constructors">Constructors</a></li>
      <li><a href="dart-core/IndexError/IndexError.html">IndexError</a></li>
    
      <li class="section-title">
        <a href="dart-core/IndexError-class.html#instance-properties">Properties</a>
      </li>
      <li><a href="dart-core/IndexError/end.html">end</a></li>
      <li><a href="dart-core/IndexError/indexable.html">indexable</a></li>
      <li><a href="dart-core/IndexError/length.html">length</a></li>
      <li><a href="dart-core/IndexError/start.html">start</a></li>
      <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
      <li class="inherited"><a href="dart-core/ArgumentError/invalidValue.html">invalidValue</a></li>
      <li class="inherited"><a href="dart-core/ArgumentError/message.html">message</a></li>
      <li class="inherited"><a href="dart-core/ArgumentError/name.html">name</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
      <li class="inherited"><a href="dart-core/Error/stackTrace.html">stackTrace</a></li>
    
      <li class="section-title inherited"><a href="dart-core/IndexError-class.html#instance-methods">Methods</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
      <li class="inherited"><a href="dart-core/ArgumentError/toString.html">toString</a></li>
    
      <li class="section-title inherited"><a href="dart-core/IndexError-class.html#operators">Operators</a></li>
      <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-left-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-constructor">IndexError</span> constructor</h1></div>

    <section class="multi-line-signature">
      
      <span class="name ">IndexError</span>(<wbr><span class="parameter" id="-param-invalidValue"><span class="type-annotation"><a href="dart-core/int-class.html">int</a></span> <span class="parameter-name">invalidValue</span>, </span> <span class="parameter" id="-param-indexable"><span class="type-annotation">dynamic</span> <span class="parameter-name">indexable</span>, [</span> <span class="parameter" id="-param-name"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">name</span>, </span> <span class="parameter" id="-param-message"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">message</span>, </span> <span class="parameter" id="-param-length"><span class="type-annotation"><a href="dart-core/int-class.html">int</a></span> <span class="parameter-name">length</span></span> ])
    </section>

    <section class="desc markdown">
      <p>Creates a new <a href="dart-core/IndexError-class.html">IndexError</a> stating that <code>invalidValue</code> is not a valid index
into <code>indexable</code>.</p>
<p>The <code>length</code> is the length of <code>indexable</code> at the time of the error.
If <code>length</code> is omitted, it defaults to <code>indexable.length</code>.</p>
<p>The message is used as part of the string representation of the error.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">IndexError(int invalidValue, dynamic indexable,
    [String? name, String? message, int? length])
    : this.indexable = indexable,
      this.length = length ?? indexable.length,
      super.value(invalidValue, name, message ?? &quot;Index out of range&quot;);</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
