<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the double constructor from the Class double class from the dart:core library, for the Dart programming language.">
  <title>double constructor - double class - dart:core library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
    <li><a href="dart-core/double-class.html">double</a></li>
    <li class="self-crumb">double constructor</li>
  </ol>
  <div class="self-name">double</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
      <li><a href="dart-core/double-class.html">double</a></li>
      <li class="self-crumb">double constructor</li>
    </ol>
    
    <h5>double class</h5>
    <ol>
    
      <li class="section-title"><a href="dart-core/double-class.html#constructors">Constructors</a></li>
      <li><a href="dart-core/double/double.html">double</a></li>
    
      <li class="section-title">
        <a href="dart-core/double-class.html#instance-properties">Properties</a>
      </li>
      <li><a href="dart-core/double/sign.html">sign</a></li>
      <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
      <li class="inherited"><a href="dart-core/num/isFinite.html">isFinite</a></li>
      <li class="inherited"><a href="dart-core/num/isInfinite.html">isInfinite</a></li>
      <li class="inherited"><a href="dart-core/num/isNaN.html">isNaN</a></li>
      <li class="inherited"><a href="dart-core/num/isNegative.html">isNegative</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
      <li class="section-title"><a href="dart-core/double-class.html#instance-methods">Methods</a></li>
      <li><a href="dart-core/double/abs.html">abs</a></li>
      <li><a href="dart-core/double/ceil.html">ceil</a></li>
      <li><a href="dart-core/double/ceilToDouble.html">ceilToDouble</a></li>
      <li><a href="dart-core/double/floor.html">floor</a></li>
      <li><a href="dart-core/double/floorToDouble.html">floorToDouble</a></li>
      <li><a href="dart-core/double/remainder.html">remainder</a></li>
      <li><a href="dart-core/double/round.html">round</a></li>
      <li><a href="dart-core/double/roundToDouble.html">roundToDouble</a></li>
      <li><a href="dart-core/double/toString.html">toString</a></li>
      <li><a href="dart-core/double/truncate.html">truncate</a></li>
      <li><a href="dart-core/double/truncateToDouble.html">truncateToDouble</a></li>
      <li class="inherited"><a href="dart-core/num/clamp.html">clamp</a></li>
      <li class="inherited"><a href="dart-core/num/compareTo.html">compareTo</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
      <li class="inherited"><a href="dart-core/num/toDouble.html">toDouble</a></li>
      <li class="inherited"><a href="dart-core/num/toInt.html">toInt</a></li>
      <li class="inherited"><a href="dart-core/num/toStringAsExponential.html">toStringAsExponential</a></li>
      <li class="inherited"><a href="dart-core/num/toStringAsFixed.html">toStringAsFixed</a></li>
      <li class="inherited"><a href="dart-core/num/toStringAsPrecision.html">toStringAsPrecision</a></li>
    
      <li class="section-title"><a href="dart-core/double-class.html#operators">Operators</a></li>
      <li><a href="dart-core/double/operator_modulo.html">operator %</a></li>
      <li><a href="dart-core/double/operator_multiply.html">operator *</a></li>
      <li><a href="dart-core/double/operator_plus.html">operator +</a></li>
      <li><a href="dart-core/double/operator_minus.html">operator -</a></li>
      <li><a href="dart-core/double/operator_divide.html">operator /</a></li>
      <li><a href="dart-core/double/operator_unary_minus.html">operator unary-</a></li>
      <li><a href="dart-core/double/operator_truncate_divide.html">operator ~/</a></li>
      <li class="inherited"><a href="dart-core/num/operator_less.html">operator <</a></li>
      <li class="inherited"><a href="dart-core/num/operator_less_equal.html">operator <=</a></li>
      <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
      <li class="inherited"><a href="dart-core/num/operator_greater.html">operator ></a></li>
      <li class="inherited"><a href="dart-core/num/operator_greater_equal.html">operator >=</a></li>
    
    
      <li class="section-title"><a href="dart-core/double-class.html#static-methods">Static methods</a></li>
      <li><a href="dart-core/double/parse.html">parse</a></li>
      <li><a href="dart-core/double/tryParse.html">tryParse</a></li>
    
      <li class="section-title"><a href="dart-core/double-class.html#constants">Constants</a></li>
      <li><a href="dart-core/double/infinity-constant.html">infinity</a></li>
      <li><a href="dart-core/double/maxFinite-constant.html">maxFinite</a></li>
      <li><a href="dart-core/double/minPositive-constant.html">minPositive</a></li>
      <li><a href="dart-core/double/nan-constant.html">nan</a></li>
      <li><a href="dart-core/double/negativeInfinity-constant.html">negativeInfinity</a></li>
    
    </ol>
  </div><!--/.sidebar-offcanvas-left-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-constructor">double</span> constructor</h1></div>

    <section class="multi-line-signature">
      
      <span class="name ">double</span>(<wbr>)
    </section>

    
    

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
