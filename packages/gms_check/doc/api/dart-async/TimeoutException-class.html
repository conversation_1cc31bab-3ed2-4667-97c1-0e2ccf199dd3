<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the TimeoutException class from the dart:async library, for the Dart programming language.">
  <title>TimeoutException class - dart:async library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-async/dart-async-library.html">dart:async</a></li>
    <li class="self-crumb">TimeoutException class</li>
  </ol>
  <div class="self-name">TimeoutException</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-async/dart-async-library.html">dart:async</a></li>
      <li class="self-crumb">TimeoutException class</li>
    </ol>
    
    <h5>dart:async library</h5>
    <ol>
      <li class="section-title"><a href="dart-async/dart-async-library.html#classes">Classes</a></li>
      <li><a href="dart-async/Completer-class.html">Completer</a></li>
      <li><a class="deprecated" href="dart-async/DeferredLibrary-class.html">DeferredLibrary</a></li>
      <li><a href="dart-async/EventSink-class.html">EventSink</a></li>
      <li><a href="dart-async/Future-class.html">Future</a></li>
      <li><a href="dart-async/FutureOr-class.html">FutureOr</a></li>
      <li><a href="dart-async/MultiStreamController-class.html">MultiStreamController</a></li>
      <li><a href="dart-async/Stream-class.html">Stream</a></li>
      <li><a href="dart-async/StreamConsumer-class.html">StreamConsumer</a></li>
      <li><a href="dart-async/StreamController-class.html">StreamController</a></li>
      <li><a href="dart-async/StreamIterator-class.html">StreamIterator</a></li>
      <li><a href="dart-async/StreamSink-class.html">StreamSink</a></li>
      <li><a href="dart-async/StreamSubscription-class.html">StreamSubscription</a></li>
      <li><a href="dart-async/StreamTransformer-class.html">StreamTransformer</a></li>
      <li><a href="dart-async/StreamTransformerBase-class.html">StreamTransformerBase</a></li>
      <li><a href="dart-async/StreamView-class.html">StreamView</a></li>
      <li><a href="dart-async/SynchronousStreamController-class.html">SynchronousStreamController</a></li>
      <li><a href="dart-async/Timer-class.html">Timer</a></li>
      <li><a href="dart-async/Zone-class.html">Zone</a></li>
      <li><a href="dart-async/ZoneDelegate-class.html">ZoneDelegate</a></li>
      <li><a href="dart-async/ZoneSpecification-class.html">ZoneSpecification</a></li>
    
      <li class="section-title"><a href="dart-async/dart-async-library.html#extension">Extensions</a></li>
      <li><a href="dart-async/FutureExtensions.html">FutureExtensions</a></li>
    
    
    
    
      <li class="section-title"><a href="dart-async/dart-async-library.html#functions">Functions</a></li>
      <li><a href="dart-async/runZoned.html">runZoned</a></li>
      <li><a href="dart-async/runZonedGuarded.html">runZonedGuarded</a></li>
      <li><a href="dart-async/scheduleMicrotask.html">scheduleMicrotask</a></li>
      <li><a href="dart-async/unawaited.html">unawaited</a></li>
    
    
      <li class="section-title"><a href="dart-async/dart-async-library.html#typedefs">Typedefs</a></li>
      <li><a href="dart-async/ControllerCallback.html">ControllerCallback</a></li>
      <li><a href="dart-async/ControllerCancelCallback.html">ControllerCancelCallback</a></li>
      <li><a href="dart-async/CreatePeriodicTimerHandler.html">CreatePeriodicTimerHandler</a></li>
      <li><a href="dart-async/CreateTimerHandler.html">CreateTimerHandler</a></li>
      <li><a href="dart-async/ErrorCallbackHandler.html">ErrorCallbackHandler</a></li>
      <li><a href="dart-async/ForkHandler.html">ForkHandler</a></li>
      <li><a href="dart-async/HandleUncaughtErrorHandler.html">HandleUncaughtErrorHandler</a></li>
      <li><a href="dart-async/PrintHandler.html">PrintHandler</a></li>
      <li><a href="dart-async/RegisterBinaryCallbackHandler.html">RegisterBinaryCallbackHandler</a></li>
      <li><a href="dart-async/RegisterCallbackHandler.html">RegisterCallbackHandler</a></li>
      <li><a href="dart-async/RegisterUnaryCallbackHandler.html">RegisterUnaryCallbackHandler</a></li>
      <li><a href="dart-async/RunBinaryHandler.html">RunBinaryHandler</a></li>
      <li><a href="dart-async/RunHandler.html">RunHandler</a></li>
      <li><a href="dart-async/RunUnaryHandler.html">RunUnaryHandler</a></li>
      <li><a href="dart-async/ScheduleMicrotaskHandler.html">ScheduleMicrotaskHandler</a></li>
      <li><a href="dart-async/ZoneBinaryCallback.html">ZoneBinaryCallback</a></li>
      <li><a href="dart-async/ZoneCallback.html">ZoneCallback</a></li>
      <li><a href="dart-async/ZoneUnaryCallback.html">ZoneUnaryCallback</a></li>
    
      <li class="section-title"><a href="dart-async/dart-async-library.html#exceptions">Exceptions</a></li>
      <li><a href="dart-async/AsyncError-class.html">AsyncError</a></li>
      <li><a href="dart-async/DeferredLoadException-class.html">DeferredLoadException</a></li>
      <li><a href="dart-async/TimeoutException-class.html">TimeoutException</a></li>
    </ol>
  </div>

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-class">TimeoutException</span> class </h1></div>

    <section class="desc markdown">
      <p>Thrown when a scheduled timeout happens while waiting for an async result.</p>
    </section>
    
    <section>
      <dl class="dl-horizontal">

        <dt>Implemented types</dt>
        <dd>
          <ul class="comma-separated clazz-relationships">
            <li><a href="dart-core/Exception-class.html">Exception</a></li>
          </ul>
        </dd>




      </dl>
    </section>

    <section class="summary offset-anchor" id="constructors">
      <h2>Constructors</h2>

      <dl class="constructor-summary-list">
        <dt id="TimeoutException" class="callable">
          <span class="name"><a href="dart-async/TimeoutException/TimeoutException.html">TimeoutException</a></span><span class="signature">(<span class="parameter" id="-param-message"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">message</span>, [</span> <span class="parameter" id="-param-duration"><span class="type-annotation"><a href="dart-core/Duration-class.html">Duration</a></span> <span class="parameter-name">duration</span></span> ])</span>
        </dt>
        <dd>
          
        </dd>
      </dl>
    </section>

    <section class="summary offset-anchor" id="instance-properties">
      <h2>Properties</h2>

      <dl class="properties">
        <dt id="duration" class="property">
          <span class="name"><a href="dart-async/TimeoutException/duration.html">duration</a></span>
          <span class="signature">&#8594; <a href="dart-core/Duration-class.html">Duration</a></span>         
        </dt>
        <dd>
          The duration that was exceeded.
                  <div class="features">final</div>
</dd>
        <dt id="message" class="property">
          <span class="name"><a href="dart-async/TimeoutException/message.html">message</a></span>
          <span class="signature">&#8594; <a href="dart-core/String-class.html">String</a></span>         
        </dt>
        <dd>
          Description of the cause of the timeout.
                  <div class="features">final</div>
</dd>
        <dt id="hashCode" class="property inherited">
          <span class="name"><a href="dart-core/Object/hashCode.html">hashCode</a></span>
          <span class="signature">&#8594; <a href="dart-core/int-class.html">int</a></span>         
        </dt>
        <dd class="inherited">
          The hash code for this object. <a href="dart-core/Object/hashCode.html">[...]</a>
                  <div class="features">read-only, inherited</div>
</dd>
        <dt id="runtimeType" class="property inherited">
          <span class="name"><a href="dart-core/Object/runtimeType.html">runtimeType</a></span>
          <span class="signature">&#8594; <a href="dart-core/Type-class.html">Type</a></span>         
        </dt>
        <dd class="inherited">
          A representation of the runtime type of the object.
                  <div class="features">read-only, inherited</div>
</dd>
      </dl>
    </section>

    <section class="summary offset-anchor" id="instance-methods">
      <h2>Methods</h2>
      <dl class="callables">
        <dt id="toString" class="callable">
          <span class="name"><a href="dart-async/TimeoutException/toString.html">toString</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-core/String-class.html">String</a></span>
          </span>
                  </dt>
        <dd>
          A string representation of this object. <a href="dart-async/TimeoutException/toString.html">[...]</a>
                  <div class="features">override</div>
</dd>
        <dt id="noSuchMethod" class="callable inherited">
          <span class="name"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></span><span class="signature">(<wbr><span class="parameter" id="noSuchMethod-param-invocation"><span class="type-annotation"><a href="dart-core/Invocation-class.html">Invocation</a></span> <span class="parameter-name">invocation</span></span>)
            <span class="returntype parameter">&#8594; dynamic</span>
          </span>
                  </dt>
        <dd class="inherited">
          Invoked when a non-existent method or property is accessed. <a href="dart-core/Object/noSuchMethod.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
      </dl>
    </section>

    <section class="summary offset-anchor inherited" id="operators">
      <h2>Operators</h2>
      <dl class="callables">
        <dt id="operator ==" class="callable inherited">
          <span class="name"><a href="dart-core/Object/operator_equals.html">operator ==</a></span><span class="signature">(<wbr><span class="parameter" id="==-param-other"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">other</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/bool-class.html">bool</a></span>
          </span>
                  </dt>
        <dd class="inherited">
          The equality operator. <a href="dart-core/Object/operator_equals.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
      </dl>
    </section>




  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
    <ol>
    
      <li class="section-title"><a href="dart-async/TimeoutException-class.html#constructors">Constructors</a></li>
      <li><a href="dart-async/TimeoutException/TimeoutException.html">TimeoutException</a></li>
    
      <li class="section-title">
        <a href="dart-async/TimeoutException-class.html#instance-properties">Properties</a>
      </li>
      <li><a href="dart-async/TimeoutException/duration.html">duration</a></li>
      <li><a href="dart-async/TimeoutException/message.html">message</a></li>
      <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
      <li class="section-title"><a href="dart-async/TimeoutException-class.html#instance-methods">Methods</a></li>
      <li><a href="dart-async/TimeoutException/toString.html">toString</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
    
      <li class="section-title inherited"><a href="dart-async/TimeoutException-class.html#operators">Operators</a></li>
      <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
