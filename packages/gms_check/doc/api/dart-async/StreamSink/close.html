<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the close method from the StreamSink class, for the Dart programming language.">
  <title>close method - StreamSink class - dart:async library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-async/dart-async-library.html">dart:async</a></li>
    <li><a href="dart-async/StreamSink-class.html">StreamSink<span class="signature">&lt;<wbr><span class="type-parameter">S</span>&gt;</span></a></li>
    <li class="self-crumb">close abstract method</li>
  </ol>
  <div class="self-name">close</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-async/dart-async-library.html">dart:async</a></li>
      <li><a href="dart-async/StreamSink-class.html">StreamSink<span class="signature">&lt;<wbr><span class="type-parameter">S</span>&gt;</span></a></li>
      <li class="self-crumb">close abstract method</li>
    </ol>
    
    <h5>StreamSink class</h5>
    <ol>
    
        <li class="section-title"><a href="dart-async/StreamSink-class.html#constructors">Constructors</a></li>
        <li><a href="dart-async/StreamSink/StreamSink.html">StreamSink</a></li>
    
        <li class="section-title">
            <a href="dart-async/StreamSink-class.html#instance-properties">Properties</a>
        </li>
        <li><a href="dart-async/StreamSink/done.html">done</a></li>
        <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
        <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
        <li class="section-title"><a href="dart-async/StreamSink-class.html#instance-methods">Methods</a></li>
        <li><a href="dart-async/StreamSink/close.html">close</a></li>
        <li class="inherited"><a href="dart-async/EventSink/add.html">add</a></li>
        <li class="inherited"><a href="dart-async/EventSink/addError.html">addError</a></li>
        <li class="inherited"><a href="dart-async/StreamConsumer/addStream.html">addStream</a></li>
        <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
        <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
    
        <li class="section-title inherited"><a href="dart-async/StreamSink-class.html#operators">Operators</a></li>
        <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-method">close</span> method</h1></div>

    <section class="multi-line-signature">
      <span class="returntype"><a href="dart-async/Future-class.html">Future</a></span>
            <span class="name ">close</span>
(<wbr>)
      <div class="features">override</div>
    </section>
    <section class="desc markdown">
      <p>Tells the stream sink that no further streams will be added.</p>
<p>This allows the stream sink to complete any remaining work and release
resources that are no longer needed</p>
<p>Returns a future which is completed when the stream sink has shut down.
If cleaning up can fail, the error may be reported in the returned future,
otherwise it completes with <code>null</code>.</p>
<p>Returns the same future as <a href="dart-async/StreamSink/done.html">done</a>.</p>
<p>The stream sink may close before the <a href="dart-async/StreamSink/close.html">close</a> method is called, either due
to an error or because it is itself providing events to someone who has
stopped listening. In that case, the <a href="dart-async/StreamSink/done.html">done</a> future is completed first,
and the <code>close</code> method will return the <code>done</code> future when called.</p>
<p>Unifies <a href="dart-async/StreamConsumer/close.html">StreamConsumer.close</a> and <a href="dart-async/EventSink/close.html">EventSink.close</a> which both mark their
object as not expecting any further events.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">Future close();</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
