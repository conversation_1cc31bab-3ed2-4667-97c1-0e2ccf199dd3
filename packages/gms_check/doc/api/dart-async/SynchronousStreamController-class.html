<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the SynchronousStreamController class from the dart:async library, for the Dart programming language.">
  <title>SynchronousStreamController class - dart:async library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-async/dart-async-library.html">dart:async</a></li>
    <li class="self-crumb">SynchronousStreamController<span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span> abstract class</li>
  </ol>
  <div class="self-name">SynchronousStreamController</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-async/dart-async-library.html">dart:async</a></li>
      <li class="self-crumb">SynchronousStreamController<span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span> abstract class</li>
    </ol>
    
    <h5>dart:async library</h5>
    <ol>
      <li class="section-title"><a href="dart-async/dart-async-library.html#classes">Classes</a></li>
      <li><a href="dart-async/Completer-class.html">Completer</a></li>
      <li><a class="deprecated" href="dart-async/DeferredLibrary-class.html">DeferredLibrary</a></li>
      <li><a href="dart-async/EventSink-class.html">EventSink</a></li>
      <li><a href="dart-async/Future-class.html">Future</a></li>
      <li><a href="dart-async/FutureOr-class.html">FutureOr</a></li>
      <li><a href="dart-async/MultiStreamController-class.html">MultiStreamController</a></li>
      <li><a href="dart-async/Stream-class.html">Stream</a></li>
      <li><a href="dart-async/StreamConsumer-class.html">StreamConsumer</a></li>
      <li><a href="dart-async/StreamController-class.html">StreamController</a></li>
      <li><a href="dart-async/StreamIterator-class.html">StreamIterator</a></li>
      <li><a href="dart-async/StreamSink-class.html">StreamSink</a></li>
      <li><a href="dart-async/StreamSubscription-class.html">StreamSubscription</a></li>
      <li><a href="dart-async/StreamTransformer-class.html">StreamTransformer</a></li>
      <li><a href="dart-async/StreamTransformerBase-class.html">StreamTransformerBase</a></li>
      <li><a href="dart-async/StreamView-class.html">StreamView</a></li>
      <li><a href="dart-async/SynchronousStreamController-class.html">SynchronousStreamController</a></li>
      <li><a href="dart-async/Timer-class.html">Timer</a></li>
      <li><a href="dart-async/Zone-class.html">Zone</a></li>
      <li><a href="dart-async/ZoneDelegate-class.html">ZoneDelegate</a></li>
      <li><a href="dart-async/ZoneSpecification-class.html">ZoneSpecification</a></li>
    
      <li class="section-title"><a href="dart-async/dart-async-library.html#extension">Extensions</a></li>
      <li><a href="dart-async/FutureExtensions.html">FutureExtensions</a></li>
    
    
    
    
      <li class="section-title"><a href="dart-async/dart-async-library.html#functions">Functions</a></li>
      <li><a href="dart-async/runZoned.html">runZoned</a></li>
      <li><a href="dart-async/runZonedGuarded.html">runZonedGuarded</a></li>
      <li><a href="dart-async/scheduleMicrotask.html">scheduleMicrotask</a></li>
      <li><a href="dart-async/unawaited.html">unawaited</a></li>
    
    
      <li class="section-title"><a href="dart-async/dart-async-library.html#typedefs">Typedefs</a></li>
      <li><a href="dart-async/ControllerCallback.html">ControllerCallback</a></li>
      <li><a href="dart-async/ControllerCancelCallback.html">ControllerCancelCallback</a></li>
      <li><a href="dart-async/CreatePeriodicTimerHandler.html">CreatePeriodicTimerHandler</a></li>
      <li><a href="dart-async/CreateTimerHandler.html">CreateTimerHandler</a></li>
      <li><a href="dart-async/ErrorCallbackHandler.html">ErrorCallbackHandler</a></li>
      <li><a href="dart-async/ForkHandler.html">ForkHandler</a></li>
      <li><a href="dart-async/HandleUncaughtErrorHandler.html">HandleUncaughtErrorHandler</a></li>
      <li><a href="dart-async/PrintHandler.html">PrintHandler</a></li>
      <li><a href="dart-async/RegisterBinaryCallbackHandler.html">RegisterBinaryCallbackHandler</a></li>
      <li><a href="dart-async/RegisterCallbackHandler.html">RegisterCallbackHandler</a></li>
      <li><a href="dart-async/RegisterUnaryCallbackHandler.html">RegisterUnaryCallbackHandler</a></li>
      <li><a href="dart-async/RunBinaryHandler.html">RunBinaryHandler</a></li>
      <li><a href="dart-async/RunHandler.html">RunHandler</a></li>
      <li><a href="dart-async/RunUnaryHandler.html">RunUnaryHandler</a></li>
      <li><a href="dart-async/ScheduleMicrotaskHandler.html">ScheduleMicrotaskHandler</a></li>
      <li><a href="dart-async/ZoneBinaryCallback.html">ZoneBinaryCallback</a></li>
      <li><a href="dart-async/ZoneCallback.html">ZoneCallback</a></li>
      <li><a href="dart-async/ZoneUnaryCallback.html">ZoneUnaryCallback</a></li>
    
      <li class="section-title"><a href="dart-async/dart-async-library.html#exceptions">Exceptions</a></li>
      <li><a href="dart-async/AsyncError-class.html">AsyncError</a></li>
      <li><a href="dart-async/DeferredLoadException-class.html">DeferredLoadException</a></li>
      <li><a href="dart-async/TimeoutException-class.html">TimeoutException</a></li>
    </ol>
  </div>

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-class">SynchronousStreamController&lt;<wbr><span class="type-parameter">T</span>&gt;</span> class </h1></div>

    <section class="desc markdown">
      <p>A stream controller that delivers its events synchronously.</p>
<p>A synchronous stream controller is intended for cases where
an already asynchronous event triggers an event on a stream.</p>
<p>Instead of adding the event to the stream in a later microtask,
causing extra latency, the event is instead fired immediately by the
synchronous stream controller, as if the stream event was
the current event or microtask.</p>
<p>The synchronous stream controller can be used to break the contract
on <a href="dart-async/Stream-class.html">Stream</a>, and it must be used carefully to avoid doing so.</p>
<p>The only advantage to using a <a href="dart-async/SynchronousStreamController-class.html">SynchronousStreamController</a> over a
normal <a href="dart-async/StreamController-class.html">StreamController</a> is the improved latency.
Only use the synchronous version if the improvement is significant,
and if its use is safe. Otherwise just use a normal stream controller,
which will always have the correct behavior for a <a href="dart-async/Stream-class.html">Stream</a>, and won't
accidentally break other code.</p>
<p>Adding events to a synchronous controller should only happen as the
very last part of the handling of the original event.
At that point, adding an event to the stream is equivalent to
returning to the event loop and adding the event in the next microtask.</p>
<p>Each listener callback will be run as if it was a top-level event
or microtask. This means that if it throws, the error will be reported as
uncaught as soon as possible.
This is one reason to add the event as the last thing in the original event
handler - any action done after adding the event will delay the report of
errors in the event listener callbacks.</p>
<p>If an event is added in a setting that isn't known to be another event,
it may cause the stream's listener to get that event before the listener
is ready to handle it. We promise that after calling <a href="dart-async/Stream/listen.html">Stream.listen</a>,
you won't get any events until the code doing the listen has completed.
Calling <a href="dart-async/SynchronousStreamController/add.html">add</a> in response to a function call of unknown origin may break
that promise.</p>
<p>An <a href="dart-async/StreamController/onListen.html">onListen</a> callback from the controller is <em>not</em> an asynchronous event,
and adding events to the controller in the <code>onListen</code> callback is always
wrong. The events will be delivered before the listener has even received
the subscription yet.</p>
<p>The synchronous broadcast stream controller also has a restrictions that a
normal stream controller does not:
The <a href="dart-async/SynchronousStreamController/add.html">add</a>, <a href="dart-async/SynchronousStreamController/addError.html">addError</a>, <a href="dart-async/SynchronousStreamController/close.html">close</a> and <a href="dart-async/StreamController/addStream.html">addStream</a> methods <em>must not</em> be
called while an event is being delivered.
That is, if a callback on a subscription on the controller's stream causes
a call to any of the functions above, the call will fail.
A broadcast stream may have more than one listener, and if an
event is added synchronously while another is being also in the process
of being added, the latter event might reach some listeners before
the former. To prevent that, an event cannot be added while a previous
event is being fired.
This guarantees that an event is fully delivered when the
first <a href="dart-async/SynchronousStreamController/add.html">add</a>, <a href="dart-async/SynchronousStreamController/addError.html">addError</a> or <a href="dart-async/SynchronousStreamController/close.html">close</a> returns,
and further events will be delivered in the correct order.</p>
<p>This still only guarantees that the event is delivered to the subscription.
If the subscription is paused, the actual callback may still happen later,
and the event will instead be buffered by the subscription.
Barring pausing, and the following buffered events that haven't been
delivered yet, callbacks will be called synchronously when an event is added.</p>
<p>Adding an event to a synchronous non-broadcast stream controller while
another event is in progress may cause the second event to be delayed
and not be delivered synchronously, and until that event is delivered,
the controller will not act synchronously.</p>
    </section>
    
    <section>
      <dl class="dl-horizontal">

        <dt>Implemented types</dt>
        <dd>
          <ul class="comma-separated clazz-relationships">
            <li><a href="dart-async/StreamController-class.html">StreamController</a><span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span></li>
          </ul>
        </dd>




      </dl>
    </section>

    <section class="summary offset-anchor" id="constructors">
      <h2>Constructors</h2>

      <dl class="constructor-summary-list">
        <dt id="SynchronousStreamController" class="callable">
          <span class="name"><a href="dart-async/SynchronousStreamController/SynchronousStreamController.html">SynchronousStreamController</a></span><span class="signature">()</span>
        </dt>
        <dd>
          
        </dd>
      </dl>
    </section>

    <section class="summary offset-anchor inherited" id="instance-properties">
      <h2>Properties</h2>

      <dl class="properties">
        <dt id="done" class="property inherited">
          <span class="name"><a href="dart-async/StreamController/done.html">done</a></span>
          <span class="signature">&#8594; <a href="dart-async/Future-class.html">Future</a></span>         
        </dt>
        <dd class="inherited">
          A future which is completed when the stream controller is done
sending events. <a href="dart-async/StreamController/done.html">[...]</a>
                  <div class="features">read-only, inherited</div>
</dd>
        <dt id="hashCode" class="property inherited">
          <span class="name"><a href="dart-core/Object/hashCode.html">hashCode</a></span>
          <span class="signature">&#8594; <a href="dart-core/int-class.html">int</a></span>         
        </dt>
        <dd class="inherited">
          The hash code for this object. <a href="dart-core/Object/hashCode.html">[...]</a>
                  <div class="features">read-only, inherited</div>
</dd>
        <dt id="hasListener" class="property inherited">
          <span class="name"><a href="dart-async/StreamController/hasListener.html">hasListener</a></span>
          <span class="signature">&#8594; <a href="dart-core/bool-class.html">bool</a></span>         
        </dt>
        <dd class="inherited">
          Whether there is a subscriber on the <a href="dart-async/Stream-class.html">Stream</a>.
                  <div class="features">read-only, inherited</div>
</dd>
        <dt id="isClosed" class="property inherited">
          <span class="name"><a href="dart-async/StreamController/isClosed.html">isClosed</a></span>
          <span class="signature">&#8594; <a href="dart-core/bool-class.html">bool</a></span>         
        </dt>
        <dd class="inherited">
          Whether the stream controller is closed for adding more events. <a href="dart-async/StreamController/isClosed.html">[...]</a>
                  <div class="features">read-only, inherited</div>
</dd>
        <dt id="isPaused" class="property inherited">
          <span class="name"><a href="dart-async/StreamController/isPaused.html">isPaused</a></span>
          <span class="signature">&#8594; <a href="dart-core/bool-class.html">bool</a></span>         
        </dt>
        <dd class="inherited">
          Whether the subscription would need to buffer events. <a href="dart-async/StreamController/isPaused.html">[...]</a>
                  <div class="features">read-only, inherited</div>
</dd>
        <dt id="onCancel" class="property inherited">
          <span class="name"><a href="dart-async/StreamController/onCancel.html">onCancel</a></span>
          <span class="signature">&#8596; <a href="dart-async/FutureOr-class.html">FutureOr</a><span class="signature">&lt;<wbr><span class="type-parameter">void</span>&gt;</span> Function<span class="signature">()</span></span>         
        </dt>
        <dd class="inherited">
          The callback which is called when the stream is canceled. <a href="dart-async/StreamController/onCancel.html">[...]</a>
                  <div class="features">read / write, inherited</div>
</dd>
        <dt id="onListen" class="property inherited">
          <span class="name"><a href="dart-async/StreamController/onListen.html">onListen</a></span>
          <span class="signature">&#8596; void Function<span class="signature">()</span></span>         
        </dt>
        <dd class="inherited">
          The callback which is called when the stream is listened to. <a href="dart-async/StreamController/onListen.html">[...]</a>
                  <div class="features">read / write, inherited</div>
</dd>
        <dt id="onPause" class="property inherited">
          <span class="name"><a href="dart-async/StreamController/onPause.html">onPause</a></span>
          <span class="signature">&#8596; void Function<span class="signature">()</span></span>         
        </dt>
        <dd class="inherited">
          The callback which is called when the stream is paused. <a href="dart-async/StreamController/onPause.html">[...]</a>
                  <div class="features">read / write, inherited</div>
</dd>
        <dt id="onResume" class="property inherited">
          <span class="name"><a href="dart-async/StreamController/onResume.html">onResume</a></span>
          <span class="signature">&#8596; void Function<span class="signature">()</span></span>         
        </dt>
        <dd class="inherited">
          The callback which is called when the stream is resumed. <a href="dart-async/StreamController/onResume.html">[...]</a>
                  <div class="features">read / write, inherited</div>
</dd>
        <dt id="runtimeType" class="property inherited">
          <span class="name"><a href="dart-core/Object/runtimeType.html">runtimeType</a></span>
          <span class="signature">&#8594; <a href="dart-core/Type-class.html">Type</a></span>         
        </dt>
        <dd class="inherited">
          A representation of the runtime type of the object.
                  <div class="features">read-only, inherited</div>
</dd>
        <dt id="sink" class="property inherited">
          <span class="name"><a href="dart-async/StreamController/sink.html">sink</a></span>
          <span class="signature">&#8594; <a href="dart-async/StreamSink-class.html">StreamSink</a><span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span></span>         
        </dt>
        <dd class="inherited">
          Returns a view of this object that only exposes the <a href="dart-async/StreamSink-class.html">StreamSink</a> interface.
                  <div class="features">read-only, inherited</div>
</dd>
        <dt id="stream" class="property inherited">
          <span class="name"><a href="dart-async/StreamController/stream.html">stream</a></span>
          <span class="signature">&#8594; <a href="dart-async/Stream-class.html">Stream</a><span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span></span>         
        </dt>
        <dd class="inherited">
          The stream that this controller is controlling.
                  <div class="features">read-only, inherited</div>
</dd>
      </dl>
    </section>

    <section class="summary offset-anchor" id="instance-methods">
      <h2>Methods</h2>
      <dl class="callables">
        <dt id="add" class="callable">
          <span class="name"><a href="dart-async/SynchronousStreamController/add.html">add</a></span><span class="signature">(<wbr><span class="parameter" id="add-param-data"><span class="type-annotation">T</span> <span class="parameter-name">data</span></span>)
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd>
          Adds event to the controller's stream. <a href="dart-async/SynchronousStreamController/add.html">[...]</a>
                  <div class="features">override</div>
</dd>
        <dt id="addError" class="callable">
          <span class="name"><a href="dart-async/SynchronousStreamController/addError.html">addError</a></span><span class="signature">(<wbr><span class="parameter" id="addError-param-error"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">error</span>, [</span> <span class="parameter" id="addError-param-stackTrace"><span class="type-annotation"><a href="dart-core/StackTrace-class.html">StackTrace</a></span> <span class="parameter-name">stackTrace</span></span> ])
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd>
          Adds error to the controller's stream. <a href="dart-async/SynchronousStreamController/addError.html">[...]</a>
                  <div class="features">override</div>
</dd>
        <dt id="close" class="callable">
          <span class="name"><a href="dart-async/SynchronousStreamController/close.html">close</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-async/Future-class.html">Future</a></span>
          </span>
                  </dt>
        <dd>
          Closes the controller's stream. <a href="dart-async/SynchronousStreamController/close.html">[...]</a>
                  <div class="features">override</div>
</dd>
        <dt id="addStream" class="callable inherited">
          <span class="name"><a href="dart-async/StreamController/addStream.html">addStream</a></span><span class="signature">(<wbr><span class="parameter" id="addStream-param-source"><span class="type-annotation"><a href="dart-async/Stream-class.html">Stream</a><span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span></span> <span class="parameter-name">source</span>, {</span> <span class="parameter" id="addStream-param-cancelOnError"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">cancelOnError</span></span> })
            <span class="returntype parameter">&#8594; <a href="dart-async/Future-class.html">Future</a></span>
          </span>
                  </dt>
        <dd class="inherited">
          Receives events from <code>source</code> and puts them into this controller's stream. <a href="dart-async/StreamController/addStream.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="noSuchMethod" class="callable inherited">
          <span class="name"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></span><span class="signature">(<wbr><span class="parameter" id="noSuchMethod-param-invocation"><span class="type-annotation"><a href="dart-core/Invocation-class.html">Invocation</a></span> <span class="parameter-name">invocation</span></span>)
            <span class="returntype parameter">&#8594; dynamic</span>
          </span>
                  </dt>
        <dd class="inherited">
          Invoked when a non-existent method or property is accessed. <a href="dart-core/Object/noSuchMethod.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="toString" class="callable inherited">
          <span class="name"><a href="dart-core/Object/toString.html">toString</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-core/String-class.html">String</a></span>
          </span>
                  </dt>
        <dd class="inherited">
          A string representation of this object. <a href="dart-core/Object/toString.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
      </dl>
    </section>

    <section class="summary offset-anchor inherited" id="operators">
      <h2>Operators</h2>
      <dl class="callables">
        <dt id="operator ==" class="callable inherited">
          <span class="name"><a href="dart-core/Object/operator_equals.html">operator ==</a></span><span class="signature">(<wbr><span class="parameter" id="==-param-other"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">other</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/bool-class.html">bool</a></span>
          </span>
                  </dt>
        <dd class="inherited">
          The equality operator. <a href="dart-core/Object/operator_equals.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
      </dl>
    </section>




  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
    <ol>
    
      <li class="section-title"><a href="dart-async/SynchronousStreamController-class.html#constructors">Constructors</a></li>
      <li><a href="dart-async/SynchronousStreamController/SynchronousStreamController.html">SynchronousStreamController</a></li>
    
      <li class="section-title inherited">
        <a href="dart-async/SynchronousStreamController-class.html#instance-properties">Properties</a>
      </li>
      <li class="inherited"><a href="dart-async/StreamController/done.html">done</a></li>
      <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
      <li class="inherited"><a href="dart-async/StreamController/hasListener.html">hasListener</a></li>
      <li class="inherited"><a href="dart-async/StreamController/isClosed.html">isClosed</a></li>
      <li class="inherited"><a href="dart-async/StreamController/isPaused.html">isPaused</a></li>
      <li class="inherited"><a href="dart-async/StreamController/onCancel.html">onCancel</a></li>
      <li class="inherited"><a href="dart-async/StreamController/onListen.html">onListen</a></li>
      <li class="inherited"><a href="dart-async/StreamController/onPause.html">onPause</a></li>
      <li class="inherited"><a href="dart-async/StreamController/onResume.html">onResume</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
      <li class="inherited"><a href="dart-async/StreamController/sink.html">sink</a></li>
      <li class="inherited"><a href="dart-async/StreamController/stream.html">stream</a></li>
    
      <li class="section-title"><a href="dart-async/SynchronousStreamController-class.html#instance-methods">Methods</a></li>
      <li><a href="dart-async/SynchronousStreamController/add.html">add</a></li>
      <li><a href="dart-async/SynchronousStreamController/addError.html">addError</a></li>
      <li><a href="dart-async/SynchronousStreamController/close.html">close</a></li>
      <li class="inherited"><a href="dart-async/StreamController/addStream.html">addStream</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
      <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
    
      <li class="section-title inherited"><a href="dart-async/SynchronousStreamController-class.html#operators">Operators</a></li>
      <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
