<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the addStream method from the StreamConsumer class, for the Dart programming language.">
  <title>addStream method - StreamConsumer class - dart:async library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-async/dart-async-library.html">dart:async</a></li>
    <li><a href="dart-async/StreamConsumer-class.html">StreamConsumer<span class="signature">&lt;<wbr><span class="type-parameter">S</span>&gt;</span></a></li>
    <li class="self-crumb">addStream abstract method</li>
  </ol>
  <div class="self-name">addStream</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-async/dart-async-library.html">dart:async</a></li>
      <li><a href="dart-async/StreamConsumer-class.html">StreamConsumer<span class="signature">&lt;<wbr><span class="type-parameter">S</span>&gt;</span></a></li>
      <li class="self-crumb">addStream abstract method</li>
    </ol>
    
    <h5>StreamConsumer class</h5>
    <ol>
    
        <li class="section-title"><a href="dart-async/StreamConsumer-class.html#constructors">Constructors</a></li>
        <li><a href="dart-async/StreamConsumer/StreamConsumer.html">StreamConsumer</a></li>
    
        <li class="section-title inherited">
            <a href="dart-async/StreamConsumer-class.html#instance-properties">Properties</a>
        </li>
        <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
        <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
        <li class="section-title"><a href="dart-async/StreamConsumer-class.html#instance-methods">Methods</a></li>
        <li><a href="dart-async/StreamConsumer/addStream.html">addStream</a></li>
        <li><a href="dart-async/StreamConsumer/close.html">close</a></li>
        <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
        <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
    
        <li class="section-title inherited"><a href="dart-async/StreamConsumer-class.html#operators">Operators</a></li>
        <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-method">addStream</span> method</h1></div>

    <section class="multi-line-signature">
      <span class="returntype"><a href="dart-async/Future-class.html">Future</a></span>
            <span class="name ">addStream</span>
(<wbr><span class="parameter" id="addStream-param-stream"><span class="type-annotation"><a href="dart-async/Stream-class.html">Stream</a><span class="signature">&lt;<wbr><span class="type-parameter">S</span>&gt;</span></span> <span class="parameter-name">stream</span></span>)
      
    </section>
    <section class="desc markdown">
      <p>Consumes the elements of <code>stream</code>.</p>
<p>Listens on <code>stream</code> and does something for each event.</p>
<p>Returns a future which is completed when the stream is done being added,
and the consumer is ready to accept a new stream.
No further calls to <a href="dart-async/StreamConsumer/addStream.html">addStream</a> or <a href="dart-async/StreamConsumer/close.html">close</a> should happen before the
returned future has completed.</p>
<p>The consumer may stop listening to the stream after an error,
it may consume all the errors and only stop at a done event,
or it may be canceled early if the receiver don't want any further events.</p>
<p>If the consumer stops listening because of some error preventing it
from continuing, it may report this error in the returned future,
otherwise it will just complete the future with <code>null</code>.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">Future addStream(Stream&lt;S&gt; stream);</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
