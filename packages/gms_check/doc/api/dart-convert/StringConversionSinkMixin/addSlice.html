<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the addSlice method from the StringConversionSinkMixin class, for the Dart programming language.">
  <title>addSlice method - StringConversionSinkMixin class - dart:convert library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-convert/dart-convert-library.html">dart:convert</a></li>
    <li><a href="dart-convert/StringConversionSinkMixin-class.html">StringConversionSinkMixin</a></li>
    <li class="self-crumb">addSlice abstract method</li>
  </ol>
  <div class="self-name">addSlice</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-convert/dart-convert-library.html">dart:convert</a></li>
      <li><a href="dart-convert/StringConversionSinkMixin-class.html">StringConversionSinkMixin</a></li>
      <li class="self-crumb">addSlice abstract method</li>
    </ol>
    
    <h5>StringConversionSinkMixin class</h5>
    <ol>
    
        <li class="section-title"><a href="dart-convert/StringConversionSinkMixin-class.html#constructors">Constructors</a></li>
        <li><a href="dart-convert/StringConversionSinkMixin/StringConversionSinkMixin.html">StringConversionSinkMixin</a></li>
    
        <li class="section-title inherited">
            <a href="dart-convert/StringConversionSinkMixin-class.html#instance-properties">Properties</a>
        </li>
        <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
        <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
        <li class="section-title"><a href="dart-convert/StringConversionSinkMixin-class.html#instance-methods">Methods</a></li>
        <li><a href="dart-convert/StringConversionSinkMixin/add.html">add</a></li>
        <li><a href="dart-convert/StringConversionSinkMixin/addSlice.html">addSlice</a></li>
        <li><a href="dart-convert/StringConversionSinkMixin/asStringSink.html">asStringSink</a></li>
        <li><a href="dart-convert/StringConversionSinkMixin/asUtf8Sink.html">asUtf8Sink</a></li>
        <li><a href="dart-convert/StringConversionSinkMixin/close.html">close</a></li>
        <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
        <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
    
        <li class="section-title inherited"><a href="dart-convert/StringConversionSinkMixin-class.html#operators">Operators</a></li>
        <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-method">addSlice</span> method</h1></div>

    <section class="multi-line-signature">
      <span class="returntype">void</span>
            <span class="name ">addSlice</span>
(<wbr><span class="parameter" id="addSlice-param-str"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">str</span>, </span> <span class="parameter" id="addSlice-param-start"><span class="type-annotation"><a href="dart-core/int-class.html">int</a></span> <span class="parameter-name">start</span>, </span> <span class="parameter" id="addSlice-param-end"><span class="type-annotation"><a href="dart-core/int-class.html">int</a></span> <span class="parameter-name">end</span>, </span> <span class="parameter" id="addSlice-param-isLast"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">isLast</span></span>)
      <div class="features">override</div>
    </section>
    <section class="desc markdown">
      <p>Adds the next <code>chunk</code> to <code>this</code>.</p>
<p>Adds the substring defined by <code>start</code> and <code>end</code>-exclusive to <code>this</code>.</p>
<p>If <code>isLast</code> is <code>true</code> closes <code>this</code>.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">void addSlice(String str, int start, int end, bool isLast);</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
