<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the JsonCodec constructor from the Class JsonCodec class from the dart:convert library, for the Dart programming language.">
  <title>JsonCodec constructor - JsonCodec class - dart:convert library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-convert/dart-convert-library.html">dart:convert</a></li>
    <li><a href="dart-convert/JsonCodec-class.html">JsonCodec</a></li>
    <li class="self-crumb">JsonCodec const constructor</li>
  </ol>
  <div class="self-name">JsonCodec</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-convert/dart-convert-library.html">dart:convert</a></li>
      <li><a href="dart-convert/JsonCodec-class.html">JsonCodec</a></li>
      <li class="self-crumb">JsonCodec const constructor</li>
    </ol>
    
    <h5>JsonCodec class</h5>
    <ol>
    
      <li class="section-title"><a href="dart-convert/JsonCodec-class.html#constructors">Constructors</a></li>
      <li><a href="dart-convert/JsonCodec/JsonCodec.html">JsonCodec</a></li>
      <li><a href="dart-convert/JsonCodec/JsonCodec.withReviver.html">withReviver</a></li>
    
      <li class="section-title">
        <a href="dart-convert/JsonCodec-class.html#instance-properties">Properties</a>
      </li>
      <li><a href="dart-convert/JsonCodec/decoder.html">decoder</a></li>
      <li><a href="dart-convert/JsonCodec/encoder.html">encoder</a></li>
      <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
      <li class="inherited"><a href="dart-convert/Codec/inverted.html">inverted</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
      <li class="section-title"><a href="dart-convert/JsonCodec-class.html#instance-methods">Methods</a></li>
      <li><a href="dart-convert/JsonCodec/decode.html">decode</a></li>
      <li><a href="dart-convert/JsonCodec/encode.html">encode</a></li>
      <li class="inherited"><a href="dart-convert/Codec/fuse.html">fuse</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
      <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
    
      <li class="section-title inherited"><a href="dart-convert/JsonCodec-class.html#operators">Operators</a></li>
      <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-left-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-constructor">JsonCodec</span> constructor</h1></div>

    <section class="multi-line-signature">
      const
      <span class="name ">JsonCodec</span>(<wbr>{<span class="parameter" id="-param-reviver"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">reviver</span>(<span class="parameter" id="param-key"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">key</span></span> <span class="parameter" id="param-value"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">value</span></span>), </span> <span class="parameter" id="-param-toEncodable"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">toEncodable</span>(<span class="parameter" id="param-object"><span class="type-annotation">dynamic</span> <span class="parameter-name">object</span></span>)</span> })
    </section>

    <section class="desc markdown">
      <p>Creates a <code>JsonCodec</code> with the given reviver and encoding function.</p>
<p>The <code>reviver</code> function is called during decoding. It is invoked once for
each object or list property that has been parsed.
The <code>key</code> argument is either the integer list index for a list property,
the string map key for object properties, or <code>null</code> for the final result.</p>
<p>If <code>reviver</code> is omitted, it defaults to returning the value argument.</p>
<p>The <code>toEncodable</code> function is used during encoding. It is invoked for
values that are not directly encodable to a string (a value that is not a
number, boolean, string, null, list or a map with string keys). The
function must return an object that is directly encodable. The elements of
a returned list and values of a returned map do not need to be directly
encodable, and if they aren't, <code>toEncodable</code> will be used on them as well.
Please notice that it is possible to cause an infinite recursive regress
in this way, by effectively creating an infinite data structure through
repeated call to <code>toEncodable</code>.</p>
<p>If <code>toEncodable</code> is omitted, it defaults to a function that returns the
result of calling <code>.toJson()</code> on the unencodable object.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">const JsonCodec(
    {Object? reviver(Object? key, Object? value)?,
    Object? toEncodable(dynamic object)?})
    : _reviver = reviver,
      _toEncodable = toEncodable;</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
