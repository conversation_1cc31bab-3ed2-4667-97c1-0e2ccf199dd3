<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the ByteConversionSink.withCallback constructor from the Class ByteConversionSink class from the dart:convert library, for the Dart programming language.">
  <title>ByteConversionSink.withCallback constructor - ByteConversionSink class - dart:convert library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-convert/dart-convert-library.html">dart:convert</a></li>
    <li><a href="dart-convert/ByteConversionSink-class.html">ByteConversionSink</a></li>
    <li class="self-crumb">ByteConversionSink.withCallback factory constructor</li>
  </ol>
  <div class="self-name">ByteConversionSink.withCallback</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-convert/dart-convert-library.html">dart:convert</a></li>
      <li><a href="dart-convert/ByteConversionSink-class.html">ByteConversionSink</a></li>
      <li class="self-crumb">ByteConversionSink.withCallback factory constructor</li>
    </ol>
    
    <h5>ByteConversionSink class</h5>
    <ol>
    
      <li class="section-title"><a href="dart-convert/ByteConversionSink-class.html#constructors">Constructors</a></li>
      <li><a href="dart-convert/ByteConversionSink/ByteConversionSink.html">ByteConversionSink</a></li>
      <li><a href="dart-convert/ByteConversionSink/ByteConversionSink.from.html">from</a></li>
      <li><a href="dart-convert/ByteConversionSink/ByteConversionSink.withCallback.html">withCallback</a></li>
    
      <li class="section-title inherited">
        <a href="dart-convert/ByteConversionSink-class.html#instance-properties">Properties</a>
      </li>
      <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
      <li class="section-title"><a href="dart-convert/ByteConversionSink-class.html#instance-methods">Methods</a></li>
      <li><a href="dart-convert/ByteConversionSink/addSlice.html">addSlice</a></li>
      <li class="inherited"><a href="dart-convert/ChunkedConversionSink/add.html">add</a></li>
      <li class="inherited"><a href="dart-convert/ChunkedConversionSink/close.html">close</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
      <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
    
      <li class="section-title inherited"><a href="dart-convert/ByteConversionSink-class.html#operators">Operators</a></li>
      <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-left-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-constructor">ByteConversionSink.withCallback</span> constructor</h1></div>

    <section class="multi-line-signature">
      
      <span class="name ">ByteConversionSink.withCallback</span>(<wbr><span class="parameter" id="withCallback-param-callback"><span class="type-annotation">void</span> <span class="parameter-name">callback</span>(<span class="parameter" id="param-accumulated"><span class="type-annotation"><a href="dart-core/List-class.html">List</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-core/int-class.html">int</a></span>&gt;</span></span> <span class="parameter-name">accumulated</span></span>)</span>)
    </section>

    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">factory ByteConversionSink.withCallback(
    void callback(List&lt;int&gt; accumulated)) = _ByteCallbackSink;</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
