<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the fuse method from the JsonEncoder class, for the Dart programming language.">
  <title>fuse method - JsonEncoder class - dart:convert library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-convert/dart-convert-library.html">dart:convert</a></li>
    <li><a href="dart-convert/JsonEncoder-class.html">JsonEncoder</a></li>
    <li class="self-crumb">fuse&lt;<wbr><span class="type-parameter">T</span>&gt; method</li>
  </ol>
  <div class="self-name">fuse</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-convert/dart-convert-library.html">dart:convert</a></li>
      <li><a href="dart-convert/JsonEncoder-class.html">JsonEncoder</a></li>
      <li class="self-crumb">fuse&lt;<wbr><span class="type-parameter">T</span>&gt; method</li>
    </ol>
    
    <h5>JsonEncoder class</h5>
    <ol>
    
        <li class="section-title"><a href="dart-convert/JsonEncoder-class.html#constructors">Constructors</a></li>
        <li><a href="dart-convert/JsonEncoder/JsonEncoder.html">JsonEncoder</a></li>
        <li><a href="dart-convert/JsonEncoder/JsonEncoder.withIndent.html">withIndent</a></li>
    
        <li class="section-title">
            <a href="dart-convert/JsonEncoder-class.html#instance-properties">Properties</a>
        </li>
        <li><a href="dart-convert/JsonEncoder/indent.html">indent</a></li>
        <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
        <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
        <li class="section-title"><a href="dart-convert/JsonEncoder-class.html#instance-methods">Methods</a></li>
        <li><a href="dart-convert/JsonEncoder/bind.html">bind</a></li>
        <li><a href="dart-convert/JsonEncoder/convert.html">convert</a></li>
        <li><a href="dart-convert/JsonEncoder/fuse.html">fuse</a></li>
        <li><a href="dart-convert/JsonEncoder/startChunkedConversion.html">startChunkedConversion</a></li>
        <li class="inherited"><a href="dart-convert/Converter/cast.html">cast</a></li>
        <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
        <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
    
        <li class="section-title inherited"><a href="dart-convert/JsonEncoder-class.html#operators">Operators</a></li>
        <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-method">fuse&lt;<wbr><span class="type-parameter">T</span>&gt;</span> method</h1></div>

    <section class="multi-line-signature">
      <span class="returntype"><a href="dart-convert/Converter-class.html">Converter</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-core/Object-class.html">Object</a></span>, <span class="type-parameter">T</span>&gt;</span></span>
            <span class="name ">fuse</span>
&lt;<wbr><span class="type-parameter">T</span>&gt;(<wbr><span class="parameter" id="fuse-param-other"><span class="type-annotation"><a href="dart-convert/Converter-class.html">Converter</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-core/String-class.html">String</a></span>, <span class="type-parameter">T</span>&gt;</span></span> <span class="parameter-name">other</span></span>)
      <div class="features">override</div>
    </section>
    <section class="desc markdown">
      <p>Fuses <code>this</code> with <code>other</code>.</p>
<p>Encoding with the resulting converter is equivalent to converting with
<code>this</code> before converting with <code>other</code>.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">Converter&lt;Object?, T&gt; fuse&lt;T&gt;(Converter&lt;String, T&gt; other) {
  if (other is Utf8Encoder) {
    &#47;&#47; The instance check guarantees that `T` is (a subtype of) List&lt;int&gt;,
    &#47;&#47; but the static type system doesn&#39;t know that, and so we cast.
    return JsonUtf8Encoder(indent, _toEncodable) as Converter&lt;Object?, T&gt;;
  }
  return super.fuse&lt;T&gt;(other);
}</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
