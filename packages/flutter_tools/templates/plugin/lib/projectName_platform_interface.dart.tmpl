import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import '{{projectName}}_method_channel.dart';

abstract class {{pluginDartClass}}Platform extends PlatformInterface {
  /// Constructs a {{pluginDartClass}}Platform.
  {{pluginDartClass}}Platform() : super(token: _token);

  static final Object _token = Object();

  static {{pluginDartClass}}Platform _instance = MethodChannel{{pluginDartClass}}();

  /// The default instance of [{{pluginDartClass}}Platform] to use.
  ///
  /// Defaults to [MethodChannel{{pluginDartClass}}].
  static {{pluginDartClass}}Platform get instance => _instance;

  /// Platform-specific implementations should set this with their own
  /// platform-specific class that extends [{{pluginDartClass}}Platform] when
  /// they register themselves.
  static set instance({{pluginDartClass}}Platform instance) {
    PlatformInterface.verifyToken(instance, _token);
    _instance = instance;
  }

  Future<String?> getPlatformVersion() {
    throw UnimplementedError('platformVersion() has not been implemented.');
  }
}
