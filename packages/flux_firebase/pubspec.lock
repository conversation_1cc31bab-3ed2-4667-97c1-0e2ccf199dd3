# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  _flutterfire_internals:
    dependency: transitive
    description:
      name: _flutterfire_internals
      sha256: e051259913915ea5bc8fe18664596bea08592fd123930605d562969cd7315fcd
      url: "https://pub.dev"
    source: hosted
    version: "1.3.51"
  animated_text_kit:
    dependency: transitive
    description:
      name: animated_text_kit
      sha256: "37392a5376c9a1a503b02463c38bc0342ef814ddbb8f9977bc90f2a84b22fa92"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.2"
  app_tracking_transparency:
    dependency: transitive
    description:
      name: app_tracking_transparency
      sha256: ce9311f0e393dbd6b1cb4aeaf609e2db8ba20b1327ca67d07c11ef4876f843a8
      url: "https://pub.dev"
    source: hosted
    version: "2.0.4"
  archive:
    dependency: transitive
    description:
      name: archive
      sha256: "22600aa1e926be775fa5fe7e6894e7fb3df9efda8891c73f70fb3262399a432d"
      url: "https://pub.dev"
    source: hosted
    version: "3.4.10"
  args:
    dependency: transitive
    description:
      name: args
      sha256: eef6c46b622e0494a36c5a12d10d77fb4e855501a91c1b9ef9339326e58f0596
      url: "https://pub.dev"
    source: hosted
    version: "2.4.2"
  asn1lib:
    dependency: transitive
    description:
      name: asn1lib
      sha256: "068190d6c99c436287936ba5855af2e1fa78d8083ae65b4db6a281780da727ae"
      url: "https://pub.dev"
    source: hosted
    version: "1.6.0"
  async:
    dependency: "direct overridden"
    description:
      name: async
      sha256: "758e6d74e971c3e5aceb4110bfd6698efc7f501675bcfe0c775459a8140750eb"
      url: "https://pub.dev"
    source: hosted
    version: "2.13.0"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "8aab1771e1243a5063b8b0ff68042d67334e3feab9e95b9490f9a6ebf73b42ea"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  cached_network_image:
    dependency: transitive
    description:
      name: cached_network_image
      sha256: "28ea9690a8207179c319965c13cd8df184d5ee721ae2ce60f398ced1219cea1f"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.1"
  cached_network_image_platform_interface:
    dependency: transitive
    description:
      name: cached_network_image_platform_interface
      sha256: "9e90e78ae72caa874a323d78fa6301b3fb8fa7ea76a8f96dc5b5bf79f283bf2f"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  cached_network_image_web:
    dependency: transitive
    description:
      name: cached_network_image_web
      sha256: "42a835caa27c220d1294311ac409a43361088625a4f23c820b006dd9bffb3316"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  carousel_slider:
    dependency: transitive
    description:
      name: carousel_slider
      sha256: "7b006ec356205054af5beaef62e2221160ea36b90fb70a35e4deacd49d0349ae"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: f71061c654a3380576a52b451dd5532377954cf9dbd272a78fc8479606670803
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  charcode:
    dependency: transitive
    description:
      name: charcode
      sha256: fb98c0f6d12c920a02ee2d998da788bca066ca5f148492b7085ee23372b12306
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: fddb70d9b5277016c77a80201021d40a2247104d9f4aa7bab7157b7e3f05b84b
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  cloud_firestore:
    dependency: "direct main"
    description:
      name: cloud_firestore
      sha256: d7204f3263ba3236c037972f1ea2821569bd7b896fa348c3d557e3b76b6dc143
      url: "https://pub.dev"
    source: hosted
    version: "5.6.3"
  cloud_firestore_platform_interface:
    dependency: "direct main"
    description:
      name: cloud_firestore_platform_interface
      sha256: "10a8519164a0e38fce52f78d540bce1170fc210d07989fe49597723400fcd0f1"
      url: "https://pub.dev"
    source: hosted
    version: "6.6.3"
  cloud_firestore_web:
    dependency: transitive
    description:
      name: cloud_firestore_web
      sha256: "4b9e34f53c32dc9891aea247d82bfb21fe7779c0064d84baea1a4f18210146de"
      url: "https://pub.dev"
    source: hosted
    version: "4.4.3"
  collection:
    dependency: "direct main"
    description:
      name: collection
      sha256: "2f5709ae4d3d59dd8f7cd309b4e023046b57d8a6c82130785d2b0e5868084e76"
      url: "https://pub.dev"
    source: hosted
    version: "1.19.1"
  convert:
    dependency: transitive
    description:
      name: convert
      sha256: "0f08b14755d163f6e2134cb58222dd25ea2a2ee8a195e53983d57c075324d592"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.1"
  country_code_picker:
    dependency: transitive
    description:
      name: country_code_picker
      sha256: "92818885f0e47486539f80463b66f649970506a91dd3c0731ca3ba5308324a4d"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  country_pickers:
    dependency: transitive
    description:
      name: country_pickers
      sha256: b10f6618fa64fbba02ffc4ad1b84dc0ca071cc206e5376de1698bddd980b355a
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  cross_file:
    dependency: transitive
    description:
      name: cross_file
      sha256: "7caf6a750a0c04effbb52a676dce9a4a592e10ad35c34d6d2d0e4811160d5670"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.4+2"
  crypto:
    dependency: transitive
    description:
      name: crypto
      sha256: ff625774173754681d66daaf4a448684fb04b78f902da9cb3d308c19cc5e8bab
      url: "https://pub.dev"
    source: hosted
    version: "3.0.3"
  csslib:
    dependency: transitive
    description:
      name: csslib
      sha256: "831883fb353c8bdc1d71979e5b342c7d88acfbc643113c14ae51e2442ea0f20f"
      url: "https://pub.dev"
    source: hosted
    version: "0.17.3"
  cupertino_icons:
    dependency: transitive
    description:
      name: cupertino_icons
      sha256: e35129dc44c9118cee2a5603506d823bab99c68393879edb440e0090d07586be
      url: "https://pub.dev"
    source: hosted
    version: "1.0.5"
  devicelocale:
    dependency: transitive
    description:
      name: devicelocale
      sha256: c198785f937840c207bd1765769faf4fda9a611507c0f11041a860a681f3adfa
      url: "https://pub.dev"
    source: hosted
    version: "0.7.0"
  dio:
    dependency: transitive
    description:
      name: dio
      sha256: "253a18bbd4851fecba42f7343a1df3a9a4c1d31a2c1b37e221086b4fa8c8dbc9"
      url: "https://pub.dev"
    source: hosted
    version: "5.8.0+1"
  dio_web_adapter:
    dependency: "direct overridden"
    description:
      name: dio_web_adapter
      sha256: e485c7a39ff2b384fa1d7e09b4e25f755804de8384358049124830b04fc4f93a
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  dotted_decoration:
    dependency: transitive
    description:
      name: dotted_decoration
      sha256: a5c5771367690b4f64ebfa7911954ab472b9675f025c373f514e32ac4bb81d5e
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  easy_debounce:
    dependency: "direct main"
    description:
      name: easy_debounce
      sha256: f082609cfb8f37defb9e37fc28bc978c6712dedf08d4c5a26f820fa10165a236
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  encrypt:
    dependency: transitive
    description:
      name: encrypt
      sha256: "62d9aa4670cc2a8798bab89b39fc71b6dfbacf615de6cf5001fb39f7e4a996a2"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.3"
  equatable:
    dependency: transitive
    description:
      name: equatable
      sha256: c2b87cb7756efdf69892005af546c56c0b5037f54d2a88269b4f347a505e3ca2
      url: "https://pub.dev"
    source: hosted
    version: "2.0.5"
  event_bus:
    dependency: transitive
    description:
      name: event_bus
      sha256: "1a55e97923769c286d295240048fc180e7b0768902c3c2e869fe059aafa15304"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  extended_image:
    dependency: transitive
    description:
      name: extended_image
      sha256: "8ad4917eaae7271ce6d975d5c0040c7903010262908fbdb49ff2798fca754d3b"
      url: "https://pub.dev"
    source: hosted
    version: "8.3.0"
  extended_image_library:
    dependency: transitive
    description:
      name: extended_image_library
      sha256: "9a94ec9314aa206cfa35f16145c3cd6e2c924badcc670eaaca8a3a8063a68cd7"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.5"
  facebook_auth_desktop:
    dependency: transitive
    description:
      name: facebook_auth_desktop
      sha256: e6cc4d6f50a1d67d99e7dac7d77a40fe27122496e224cb708ae168d7d9aac0ac
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "5368f224a74523e8d2e7399ea1638b37aecfca824a3cc4dfdf77bf1fa905ac44"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.3"
  ffi:
    dependency: transitive
    description:
      name: ffi
      sha256: "16ed7b077ef01ad6170a3d0c57caa4a112a38d7a2ed5602e0aca9ca6f3d98da6"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.3"
  file:
    dependency: transitive
    description:
      name: file
      sha256: "1b92bec4fc2a72f59a8e15af5f52cd441e4a7860b49499d69dfa817af20e925d"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.4"
  file_selector_linux:
    dependency: transitive
    description:
      name: file_selector_linux
      sha256: d17c5e450192cdc40b718804dfb4eaf79a71bed60ee9530703900879ba50baa3
      url: "https://pub.dev"
    source: hosted
    version: "0.9.1+3"
  file_selector_macos:
    dependency: transitive
    description:
      name: file_selector_macos
      sha256: "6290eec24fc4cc62535fe609e0c6714d3c1306191dc8c3b0319eaecc09423a3a"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.2"
  file_selector_platform_interface:
    dependency: transitive
    description:
      name: file_selector_platform_interface
      sha256: "2a7f4bbf7bd2f022ecea85bfb1754e87f7dd403a9abc17a84a4fa2ddfe2abc0a"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.1"
  file_selector_windows:
    dependency: transitive
    description:
      name: file_selector_windows
      sha256: ef246380b66d1fb9089fc65622c387bf3780bca79f533424c31d07f12c2c7fd8
      url: "https://pub.dev"
    source: hosted
    version: "0.9.2"
  firebase_analytics:
    dependency: "direct main"
    description:
      name: firebase_analytics
      sha256: "47428047a0778f72af53a3c7cb5d556e1cb25e2327cc8aa40d544971dc6245b2"
      url: "https://pub.dev"
    source: hosted
    version: "11.4.2"
  firebase_analytics_platform_interface:
    dependency: "direct main"
    description:
      name: firebase_analytics_platform_interface
      sha256: "1076f4b041f76143e14878c70f0758f17fe5910c0cd992db9e93bd3c3584512b"
      url: "https://pub.dev"
    source: hosted
    version: "4.3.2"
  firebase_analytics_web:
    dependency: transitive
    description:
      name: firebase_analytics_web
      sha256: "8f6dd64ea6d28b7f5b9e739d183a9e1c7f17027794a3e9aba1879621d42426ef"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.10+8"
  firebase_auth:
    dependency: "direct main"
    description:
      name: firebase_auth
      sha256: "2886a01a895565722add556510263231390a9f1d1d51eee34c22f9b94a73dd54"
      url: "https://pub.dev"
    source: hosted
    version: "5.4.2"
  firebase_auth_platform_interface:
    dependency: "direct main"
    description:
      name: firebase_auth_platform_interface
      sha256: "2e8fe7e6b5869c981f62c0de1a0abef6f626a1daffe92e1e6881448a9d3da778"
      url: "https://pub.dev"
    source: hosted
    version: "7.5.2"
  firebase_auth_web:
    dependency: transitive
    description:
      name: firebase_auth_web
      sha256: c9600115b6f74365a51c735d4c43d4632ad44bfde505fe7c13c838701cd01ff2
      url: "https://pub.dev"
    source: hosted
    version: "5.13.8"
  firebase_core:
    dependency: "direct main"
    description:
      name: firebase_core
      sha256: "93dc4dd12f9b02c5767f235307f609e61ed9211047132d07f9e02c668f0bfc33"
      url: "https://pub.dev"
    source: hosted
    version: "3.11.0"
  firebase_core_platform_interface:
    dependency: "direct main"
    description:
      name: firebase_core_platform_interface
      sha256: d7253d255ff10f85cfd2adaba9ac17bae878fa3ba577462451163bd9f1d1f0bf
      url: "https://pub.dev"
    source: hosted
    version: "5.4.0"
  firebase_core_web:
    dependency: transitive
    description:
      name: firebase_core_web
      sha256: "0e13c80f0de8acaa5d0519cbe23c8b4cc138a2d5d508b5755c861bdfc9762678"
      url: "https://pub.dev"
    source: hosted
    version: "2.20.0"
  firebase_dynamic_links:
    dependency: "direct main"
    description:
      name: firebase_dynamic_links
      sha256: f3b546180b1920ffd366623de384e906d3eccd4368efa8cf155476b0f4780064
      url: "https://pub.dev"
    source: hosted
    version: "6.1.2"
  firebase_dynamic_links_platform_interface:
    dependency: transitive
    description:
      name: firebase_dynamic_links_platform_interface
      sha256: "95ea6f6c5b4b62aa05e312aff30f423e957c2bed3dcf3baa6d262e936021d3a1"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.7+2"
  firebase_messaging:
    dependency: "direct main"
    description:
      name: firebase_messaging
      sha256: "3dee3b0cbfe719e64773cb7d1cad57c58b2235a8c136f5715fe733a54058c783"
      url: "https://pub.dev"
    source: hosted
    version: "15.2.2"
  firebase_messaging_platform_interface:
    dependency: "direct main"
    description:
      name: firebase_messaging_platform_interface
      sha256: e9ea726b9bb864fc6223bb66422bd9877b9973ae51967754a769b0d01e201c1e
      url: "https://pub.dev"
    source: hosted
    version: "4.6.2"
  firebase_messaging_web:
    dependency: transitive
    description:
      name: firebase_messaging_web
      sha256: "5f7b40e8bf861a37f8b8196e347d8a919750421a45f0b45d1bb74e98fa72726e"
      url: "https://pub.dev"
    source: hosted
    version: "3.10.2"
  firebase_remote_config:
    dependency: "direct main"
    description:
      name: firebase_remote_config
      sha256: "5cfc2d26bb8caba2bfeeb88cbf589cb1c00031eb783019f37c36e12381b8d9ca"
      url: "https://pub.dev"
    source: hosted
    version: "5.4.0"
  firebase_remote_config_platform_interface:
    dependency: "direct main"
    description:
      name: firebase_remote_config_platform_interface
      sha256: "79627889fe98b989825e23154f0c6392f9d463b4e39945b27953b34941109fca"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.0"
  firebase_remote_config_web:
    dependency: transitive
    description:
      name: firebase_remote_config_web
      sha256: d5c999f9398f454ca1e60d3410df6b2a268ff532e6146303aa9dd7b43434afe3
      url: "https://pub.dev"
    source: hosted
    version: "1.8.0"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      sha256: "25517a4deb0c03aa0f32fd12db525856438902d9c16536311e76cdc57b31d7d1"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  flash:
    dependency: transitive
    description:
      name: flash
      sha256: "1bb5a53158d7834ce0db8b15a0b948ee369698e59c907b751035c3a787f347ac"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_animate:
    dependency: transitive
    description:
      name: flutter_animate
      sha256: "7c8a6594a9252dad30cc2ef16e33270b6248c4dedc3b3d06c86c4f3f4dc05ae5"
      url: "https://pub.dev"
    source: hosted
    version: "4.5.0"
  flutter_async_autocomplete:
    dependency: transitive
    description:
      name: flutter_async_autocomplete
      sha256: "573c1480c8430ced699199c7a6fa9a4f3876ee908ee947bc7b9b5b0bf6c42bc0"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.1"
  flutter_cache_manager:
    dependency: transitive
    description:
      name: flutter_cache_manager
      sha256: "8207f27539deb83732fdda03e259349046a39a4c767269285f449ade355d54ba"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.1"
  flutter_custom_clippers:
    dependency: transitive
    description:
      name: flutter_custom_clippers
      sha256: "473e3daf61c2a6cee0ad137393259a25223239d519a131c7ec1cac04d06e5407"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  flutter_direct_caller_plugin:
    dependency: transitive
    description:
      name: flutter_direct_caller_plugin
      sha256: "0dc32a2cb351f41ea633a9c9e6be5eb5239197f390e519f3a991bda794bba0cd"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.4"
  flutter_facebook_auth:
    dependency: transitive
    description:
      name: flutter_facebook_auth
      sha256: "4958d39b62791d8f08c429b5c296e9e27f850e3385d63ebc9fe7b69f2c243c6c"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.1"
  flutter_facebook_auth_platform_interface:
    dependency: transitive
    description:
      name: flutter_facebook_auth_platform_interface
      sha256: "86630c4dbba1c20fba26ea9e59ad0d48f5ff59e7373cacd36f916160186f9ce9"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  flutter_facebook_auth_web:
    dependency: transitive
    description:
      name: flutter_facebook_auth_web
      sha256: "22dca8091409309ad85b9f430fbd8f57b686276979da5195e7e97587352567ce"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  flutter_hooks:
    dependency: transitive
    description:
      name: flutter_hooks
      sha256: cde36b12f7188c85286fba9b38cc5a902e7279f36dd676967106c041dc9dde70
      url: "https://pub.dev"
    source: hosted
    version: "0.20.5"
  flutter_inappwebview:
    dependency: transitive
    description:
      name: flutter_inappwebview
      sha256: "80092d13d3e29b6227e25b67973c67c7210bd5e35c4b747ca908e31eb71a46d5"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.5"
  flutter_inappwebview_android:
    dependency: transitive
    description:
      name: flutter_inappwebview_android
      sha256: "62557c15a5c2db5d195cb3892aab74fcaec266d7b86d59a6f0027abd672cddba"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.3"
  flutter_inappwebview_internal_annotations:
    dependency: transitive
    description:
      name: flutter_inappwebview_internal_annotations
      sha256: "5f80fd30e208ddded7dbbcd0d569e7995f9f63d45ea3f548d8dd4c0b473fb4c8"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  flutter_inappwebview_ios:
    dependency: transitive
    description:
      name: flutter_inappwebview_ios
      sha256: "5818cf9b26cf0cbb0f62ff50772217d41ea8d3d9cc00279c45f8aabaa1b4025d"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  flutter_inappwebview_macos:
    dependency: transitive
    description:
      name: flutter_inappwebview_macos
      sha256: c1fbb86af1a3738e3541364d7d1866315ffb0468a1a77e34198c9be571287da1
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  flutter_inappwebview_platform_interface:
    dependency: transitive
    description:
      name: flutter_inappwebview_platform_interface
      sha256: cf5323e194096b6ede7a1ca808c3e0a078e4b33cc3f6338977d75b4024ba2500
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0+1"
  flutter_inappwebview_web:
    dependency: transitive
    description:
      name: flutter_inappwebview_web
      sha256: "55f89c83b0a0d3b7893306b3bb545ba4770a4df018204917148ebb42dc14a598"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  flutter_inappwebview_windows:
    dependency: transitive
    description:
      name: flutter_inappwebview_windows
      sha256: "8b4d3a46078a2cdc636c4a3d10d10f2a16882f6be607962dbfff8874d1642055"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.0"
  flutter_linkify:
    dependency: transitive
    description:
      name: flutter_linkify
      sha256: "74669e06a8f358fee4512b4320c0b80e51cffc496607931de68d28f099254073"
      url: "https://pub.dev"
    source: hosted
    version: "6.0.0"
  flutter_localization:
    dependency: transitive
    description:
      name: flutter_localization
      sha256: faaeb1eba307473032e2c2af737f36ced61fc98735608410d0a6d9c231b50912
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0"
  flutter_localizations:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_plugin_android_lifecycle:
    dependency: transitive
    description:
      name: flutter_plugin_android_lifecycle
      sha256: "950e77c2bbe1692bc0874fc7fb491b96a4dc340457f4ea1641443d0a6c1ea360"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.15"
  flutter_rating_bar:
    dependency: transitive
    description:
      name: flutter_rating_bar
      sha256: d2af03469eac832c591a1eba47c91ecc871fe5708e69967073c043b2d775ed93
      url: "https://pub.dev"
    source: hosted
    version: "4.0.1"
  flutter_secure_storage:
    dependency: transitive
    description:
      name: flutter_secure_storage
      sha256: "9cad52d75ebc511adfae3d447d5d13da15a55a92c9410e50f67335b6d21d16ea"
      url: "https://pub.dev"
    source: hosted
    version: "9.2.4"
  flutter_secure_storage_linux:
    dependency: transitive
    description:
      name: flutter_secure_storage_linux
      sha256: bf7404619d7ab5c0a1151d7c4e802edad8f33535abfbeff2f9e1fe1274e2d705
      url: "https://pub.dev"
    source: hosted
    version: "1.2.2"
  flutter_secure_storage_macos:
    dependency: transitive
    description:
      name: flutter_secure_storage_macos
      sha256: "6c0a2795a2d1de26ae202a0d78527d163f4acbb11cde4c75c670f3a0fc064247"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.3"
  flutter_secure_storage_platform_interface:
    dependency: transitive
    description:
      name: flutter_secure_storage_platform_interface
      sha256: cf91ad32ce5adef6fba4d736a542baca9daf3beac4db2d04be350b87f69ac4a8
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  flutter_secure_storage_web:
    dependency: transitive
    description:
      name: flutter_secure_storage_web
      sha256: f4ebff989b4f07b2656fb16b47852c0aab9fed9b4ec1c70103368337bc1886a9
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  flutter_secure_storage_windows:
    dependency: transitive
    description:
      name: flutter_secure_storage_windows
      sha256: b20b07cb5ed4ed74fc567b78a72936203f587eba460af1df11281c9326cd3709
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
  flutter_shaders:
    dependency: transitive
    description:
      name: flutter_shaders
      sha256: "02750b545c01ff4d8e9bbe8f27a7731aa3778402506c67daa1de7f5fc3f4befe"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.2"
  flutter_spinkit:
    dependency: transitive
    description:
      name: flutter_spinkit
      sha256: b39c753e909d4796906c5696a14daf33639a76e017136c8d82bf3e620ce5bb8e
      url: "https://pub.dev"
    source: hosted
    version: "5.2.0"
  flutter_staggered_grid_view:
    dependency: transitive
    description:
      name: flutter_staggered_grid_view
      sha256: "19e7abb550c96fbfeb546b23f3ff356ee7c59a019a651f8f102a4ba9b7349395"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.0"
  flutter_svg:
    dependency: transitive
    description:
      name: flutter_svg
      sha256: c200fd79c918a40c5cd50ea0877fa13f81bdaf6f0a5d3dbcc2a13e3285d6aa1b
      url: "https://pub.dev"
    source: hosted
    version: "2.0.17"
  flutter_swiper_null_safety:
    dependency: transitive
    description:
      path: "."
      ref: "18e8d2d642ff9e0013fc4477c6d64c52732b463a"
      resolved-ref: "18e8d2d642ff9e0013fc4477c6d64c52732b463a"
      url: "https://github.com/inspireui/flutter_swiper_null_safety"
    source: git
    version: "1.0.2"
  flutter_test:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_widget_from_html_core:
    dependency: transitive
    description:
      name: flutter_widget_from_html_core
      sha256: f77ea1aa1ba29a38fcce04483f44f12382f541b9e8c2150df37166c23bbbd30f
      url: "https://pub.dev"
    source: hosted
    version: "0.16.0"
  flutter_zoom_drawer:
    dependency: transitive
    description:
      name: flutter_zoom_drawer
      sha256: "5a3708548868463fb36e0e3171761ab7cb513df88d2f14053802812d2e855060"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.0"
  font_awesome_flutter:
    dependency: transitive
    description:
      name: font_awesome_flutter
      sha256: d3a89184101baec7f4600d58840a764d2ef760fe1c5a20ef9e6b0e9b24a07a3a
      url: "https://pub.dev"
    source: hosted
    version: "10.8.0"
  freezed_annotation:
    dependency: transitive
    description:
      name: freezed_annotation
      sha256: c3fd9336eb55a38cc1bbd79ab17573113a8deccd0ecbbf926cca3c62803b5c2d
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  fstore:
    dependency: "direct main"
    description:
      path: "../.."
      relative: true
    source: path
    version: "1.0.22+122"
  fwfh_cached_network_image:
    dependency: transitive
    description:
      name: fwfh_cached_network_image
      sha256: "8f4896109ff3e42424ccacf9058ba3afe5d575b58946c8ac646ac85ae882ce23"
      url: "https://pub.dev"
    source: hosted
    version: "0.16.0"
  fwfh_svg:
    dependency: transitive
    description:
      name: fwfh_svg
      sha256: "82f3eb378186fe39b3e2e01ed48a1830d34b0b9a237d951077e74ff0d99e2ac3"
      url: "https://pub.dev"
    source: hosted
    version: "0.16.0"
  fwfh_webview:
    dependency: transitive
    description:
      name: fwfh_webview
      sha256: "894aa7d98ebdc2d86d79ac2309173043dec7f102575de87bf9626ddb26104e49"
      url: "https://pub.dev"
    source: hosted
    version: "0.15.4"
  get:
    dependency: transitive
    description:
      name: get
      sha256: e4e7335ede17452b391ed3b2ede016545706c01a02292a6c97619705e7d2a85e
      url: "https://pub.dev"
    source: hosted
    version: "4.6.6"
  get_it:
    dependency: transitive
    description:
      name: get_it
      sha256: e6017ce7fdeaf218dc51a100344d8cb70134b80e28b760f8bb23c242437bafd7
      url: "https://pub.dev"
    source: hosted
    version: "7.6.7"
  get_storage:
    dependency: transitive
    description:
      name: get_storage
      sha256: "39db1fffe779d0c22b3a744376e86febe4ade43bf65e06eab5af707dc84185a2"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  gms_check:
    dependency: transitive
    description:
      path: "../gms_check"
      relative: true
    source: path
    version: "1.0.2"
  google_fonts:
    dependency: transitive
    description:
      name: google_fonts
      sha256: f0b8d115a13ecf827013ec9fc883390ccc0e87a96ed5347a3114cac177ef18e8
      url: "https://pub.dev"
    source: hosted
    version: "6.1.0"
  google_identity_services_web:
    dependency: transitive
    description:
      name: google_identity_services_web
      sha256: "7940fdc3b1035db4d65d387c1bdd6f9574deaa6777411569c05ecc25672efacd"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1"
  google_maps:
    dependency: transitive
    description:
      name: google_maps
      sha256: "4d6e199c561ca06792c964fa24b2bac7197bf4b401c2e1d23e345e5f9939f531"
      url: "https://pub.dev"
    source: hosted
    version: "8.1.1"
  google_maps_flutter:
    dependency: transitive
    description:
      name: google_maps_flutter
      sha256: "209856c8e5571626afba7182cf634b2910069dc567954e76ec3e3fb37f5e9db3"
      url: "https://pub.dev"
    source: hosted
    version: "2.10.0"
  google_maps_flutter_android:
    dependency: transitive
    description:
      name: google_maps_flutter_android
      sha256: bccf64ccbb2ea672dc62a61177b315a340af86b0228564484b023657544a3fd5
      url: "https://pub.dev"
    source: hosted
    version: "2.14.11"
  google_maps_flutter_ios:
    dependency: transitive
    description:
      name: google_maps_flutter_ios
      sha256: "753ebf6a2bc24c5eba8e714c901345d858abd9694b1f878c43614fd3f06b8060"
      url: "https://pub.dev"
    source: hosted
    version: "2.13.1"
  google_maps_flutter_platform_interface:
    dependency: transitive
    description:
      name: google_maps_flutter_platform_interface
      sha256: a951981c22d790848efb9f114f81794945bc5c06bc566238a419a92f110af6cb
      url: "https://pub.dev"
    source: hosted
    version: "2.9.5"
  google_maps_flutter_web:
    dependency: transitive
    description:
      name: google_maps_flutter_web
      sha256: ff39211bd25d7fad125d19f757eba85bd154460907cd4d135e07e3d0f98a4130
      url: "https://pub.dev"
    source: hosted
    version: "0.5.10"
  google_mobile_ads:
    dependency: transitive
    description:
      name: google_mobile_ads
      sha256: "0d4a3744b5e8ed1b8be6a1b452d309f811688855a497c6113fc4400f922db603"
      url: "https://pub.dev"
    source: hosted
    version: "5.3.1"
  google_sign_in:
    dependency: transitive
    description:
      name: google_sign_in
      sha256: "0b8787cb9c1a68ad398e8010e8c8766bfa33556d2ab97c439fb4137756d7308f"
      url: "https://pub.dev"
    source: hosted
    version: "6.2.1"
  google_sign_in_android:
    dependency: transitive
    description:
      name: google_sign_in_android
      sha256: f58a17ac07d783d000786a6c313fa4a0d2ee599a346d69b24fc48fb378d5d150
      url: "https://pub.dev"
    source: hosted
    version: "6.1.16"
  google_sign_in_ios:
    dependency: transitive
    description:
      name: google_sign_in_ios
      sha256: a7d653803468d30b82ceb47ea00fe86d23c56e63eb2e5c2248bb68e9df203217
      url: "https://pub.dev"
    source: hosted
    version: "5.7.4"
  google_sign_in_platform_interface:
    dependency: transitive
    description:
      name: google_sign_in_platform_interface
      sha256: e69553c0fc6a76216e9d06a8c3767e291ad9be42171f879aab7ab708569d4393
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  google_sign_in_web:
    dependency: transitive
    description:
      name: google_sign_in_web
      sha256: "69b9ce0e760945ff52337921a8b5871592b74c92f85e7632293310701eea68cc"
      url: "https://pub.dev"
    source: hosted
    version: "0.12.0+2"
  googleapis_auth:
    dependency: transitive
    description:
      name: googleapis_auth
      sha256: af7c3a3edf9d0de2e1e0a77e994fae0a581c525fa7012af4fa0d4a52ed9484da
      url: "https://pub.dev"
    source: hosted
    version: "1.4.1"
  hive:
    dependency: transitive
    description:
      name: hive
      sha256: "8dcf6db979d7933da8217edcec84e9df1bdb4e4edc7fc77dbd5aa74356d6d941"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.3"
  hive_flutter:
    dependency: transitive
    description:
      name: hive_flutter
      sha256: dca1da446b1d808a51689fb5d0c6c9510c0a2ba01e22805d492c73b68e33eecc
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  html:
    dependency: transitive
    description:
      name: html
      sha256: "3a7812d5bcd2894edf53dfaf8cd640876cf6cef50a8f238745c8b8120ea74d3a"
      url: "https://pub.dev"
    source: hosted
    version: "0.15.4"
  html_unescape:
    dependency: transitive
    description:
      name: html_unescape
      sha256: "15362d7a18f19d7b742ef8dcb811f5fd2a2df98db9f80ea393c075189e0b61e3"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  http:
    dependency: transitive
    description:
      name: http
      sha256: "759d1a329847dd0f39226c688d3e06a6b8679668e350e2891a6474f8b4bb8525"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  http_auth:
    dependency: transitive
    description:
      name: http_auth
      sha256: b7625acba2987fa69140d9600c679819f33227d665f525fbb2f394e08cf917d1
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  http_client_helper:
    dependency: transitive
    description:
      name: http_client_helper
      sha256: "8a9127650734da86b5c73760de2b404494c968a3fd55602045ffec789dac3cb1"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      sha256: "2aa08ce0341cc9b354a498388e30986515406668dbcc4f7c950c3e715496693b"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.2"
  image_picker:
    dependency: transitive
    description:
      name: image_picker
      sha256: "021834d9c0c3de46bf0fe40341fa07168407f694d9b2bb18d532dc1261867f7a"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  image_picker_android:
    dependency: transitive
    description:
      name: image_picker_android
      sha256: "8179b54039b50eee561676232304f487602e2950ffb3e8995ed9034d6505ca34"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.7+4"
  image_picker_for_web:
    dependency: transitive
    description:
      name: image_picker_for_web
      sha256: "869fe8a64771b7afbc99fc433a5f7be2fea4d1cb3d7c11a48b6b579eb9c797f0"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  image_picker_ios:
    dependency: transitive
    description:
      name: image_picker_ios
      sha256: bee3b7c7e6266a37fb1bfdc275bc60427029d447205d44933aac130fbb9d3330
      url: "https://pub.dev"
    source: hosted
    version: "0.8.8+1"
  image_picker_linux:
    dependency: transitive
    description:
      name: image_picker_linux
      sha256: "02cbc21fe1706b97942b575966e5fbbeaac535e76deef70d3a242e4afb857831"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1"
  image_picker_macos:
    dependency: transitive
    description:
      name: image_picker_macos
      sha256: cee2aa86c56780c13af2c77b5f2f72973464db204569e1ba2dd744459a065af4
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1"
  image_picker_platform_interface:
    dependency: transitive
    description:
      name: image_picker_platform_interface
      sha256: "886d57f0be73c4b140004e78b9f28a8914a09e50c2d816bdd0520051a71236a0"
      url: "https://pub.dev"
    source: hosted
    version: "2.10.1"
  image_picker_windows:
    dependency: transitive
    description:
      name: image_picker_windows
      sha256: c3066601ea42113922232c7b7b3330a2d86f029f685bba99d82c30e799914952
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1"
  in_app_update:
    dependency: transitive
    description:
      name: in_app_update
      sha256: b6ccb757281a96a4b18536f68fe2567aeca865134218719364212da8fe94615c
      url: "https://pub.dev"
    source: hosted
    version: "4.2.2"
  infinite_carousel:
    dependency: transitive
    description:
      name: infinite_carousel
      sha256: "572967b028f4803e5f4be9adee5760b6c4834eb6912b0f2292f2d93a51912ba0"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  inspireui:
    dependency: "direct main"
    description:
      path: "../inspireui"
      relative: true
    source: path
    version: "2.2.7"
  intl:
    dependency: "direct main"
    description:
      name: intl
      sha256: "3df61194eb431efc39c4ceba583b95633a403f46c9fd341e550ce0bfa50e9aa5"
      url: "https://pub.dev"
    source: hosted
    version: "0.20.2"
  intl_phone_number_input:
    dependency: transitive
    description:
      path: "."
      ref: "1a3fa368a009eac3cf02a91052956981da10e725"
      resolved-ref: "1a3fa368a009eac3cf02a91052956981da10e725"
      url: "https://github.com/inspireui/intl_phone_number_input"
    source: git
    version: "0.7.3+1"
  intrinsic_grid_view:
    dependency: transitive
    description:
      name: intrinsic_grid_view
      sha256: c86b77d569af3b2420fcfd3dbcd6ec1e83dab57b08e49acb05e2d257510652d2
      url: "https://pub.dev"
    source: hosted
    version: "0.0.2"
  intro_slider:
    dependency: transitive
    description:
      name: intro_slider
      sha256: eb43fefa27b0655edebc3e7fe7ff320cb5996a4e64649d79969c7099105fe52f
      url: "https://pub.dev"
    source: hosted
    version: "4.2.1"
  js:
    dependency: transitive
    description:
      name: js
      sha256: f2c445dce49627136094980615a031419f7f3eb393237e4ecd97ac15dea343f3
      url: "https://pub.dev"
    source: hosted
    version: "0.6.7"
  json_annotation:
    dependency: transitive
    description:
      name: json_annotation
      sha256: "1ce844379ca14835a50d2f019a3099f419082cfdd231cd86a142af94dd5c6bb1"
      url: "https://pub.dev"
    source: hosted
    version: "4.9.0"
  jumping_dot:
    dependency: transitive
    description:
      name: jumping_dot
      sha256: a2f755140da73d018bf5198acd83cdd2898a393e5ea45ee94250b9ba44566d1c
      url: "https://pub.dev"
    source: hosted
    version: "0.0.6"
  leak_tracker:
    dependency: transitive
    description:
      name: leak_tracker
      sha256: "6bb818ecbdffe216e81182c2f0714a2e62b593f4a4f13098713ff1685dfb6ab0"
      url: "https://pub.dev"
    source: hosted
    version: "10.0.9"
  leak_tracker_flutter_testing:
    dependency: transitive
    description:
      name: leak_tracker_flutter_testing
      sha256: f8b613e7e6a13ec79cfdc0e97638fddb3ab848452eff057653abd3edba760573
      url: "https://pub.dev"
    source: hosted
    version: "3.0.9"
  leak_tracker_testing:
    dependency: transitive
    description:
      name: leak_tracker_testing
      sha256: "6ba465d5d76e67ddf503e1161d1f4a6bc42306f9d66ca1e8f079a47290fb06d3"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  libphonenumber_platform_interface:
    dependency: transitive
    description:
      name: libphonenumber_platform_interface
      sha256: b8c57fc74bf61491672b8cd94039e35359c6e0d79a88018c1cc7facb76771a4b
      url: "https://pub.dev"
    source: hosted
    version: "0.4.1"
  libphonenumber_plugin:
    dependency: transitive
    description:
      name: libphonenumber_plugin
      sha256: d95a0f799936e6e243cd0be151af9a6f09286bbb2c513d517dad548c4b3eb70e
      url: "https://pub.dev"
    source: hosted
    version: "0.3.2"
  libphonenumber_web:
    dependency: transitive
    description:
      name: libphonenumber_web
      sha256: "3eb541422e581d198be4d59293cb5902e4057ae6bb0822eaddf9a0f98952d222"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.1"
  linkify:
    dependency: transitive
    description:
      name: linkify
      sha256: "4139ea77f4651ab9c315b577da2dd108d9aa0bd84b5d03d33323f1970c645832"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  local_auth:
    dependency: transitive
    description:
      name: local_auth
      sha256: "434d854cf478f17f12ab29a76a02b3067f86a63a6d6c4eb8fbfdcfe4879c1b7b"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  local_auth_android:
    dependency: transitive
    description:
      name: local_auth_android
      sha256: "0abe4e72f55c785b28900de52a2522c86baba0988838b5dc22241b072ecccd74"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.48"
  local_auth_darwin:
    dependency: transitive
    description:
      name: local_auth_darwin
      sha256: "630996cd7b7f28f5ab92432c4b35d055dd03a747bc319e5ffbb3c4806a3e50d2"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.3"
  local_auth_platform_interface:
    dependency: transitive
    description:
      name: local_auth_platform_interface
      sha256: "9e160d59ef0743e35f1b50f4fb84dc64f55676b1b8071e319ef35e7f3bc13367"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.7"
  local_auth_windows:
    dependency: transitive
    description:
      name: local_auth_windows
      sha256: "19323b75ab781d5362dbb15dcb7e0916d2431c7a6dbdda016ec9708689877f73"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.8"
  location:
    dependency: transitive
    description:
      name: location
      sha256: "06be54f682c9073cbfec3899eb9bc8ed90faa0e17735c9d9fa7fe426f5be1dd1"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.3"
  location_platform_interface:
    dependency: transitive
    description:
      name: location_platform_interface
      sha256: "8aa1d34eeecc979d7c9fe372931d84f6d2ebbd52226a54fe1620de6fdc0753b1"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
  location_web:
    dependency: transitive
    description:
      name: location_web
      sha256: ec484c66e8a4ff1ee5d044c203f4b6b71e3a0556a97b739a5bc9616de672412b
      url: "https://pub.dev"
    source: hosted
    version: "4.2.0"
  logger:
    dependency: transitive
    description:
      name: logger
      sha256: be4b23575aac7ebf01f225a241eb7f6b5641eeaf43c6a8613510fc2f8cf187d1
      url: "https://pub.dev"
    source: hosted
    version: "2.5.0"
  logging:
    dependency: transitive
    description:
      name: logging
      sha256: "623a88c9594aa774443aa3eb2d41807a48486b5613e67599fb4c41c0ad47c340"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  lottie:
    dependency: transitive
    description:
      name: lottie
      sha256: ce2bb2605753915080e4ee47f036a64228c88dc7f56f7bc1dbe912d75b55b1e2
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: dc58c723c3c24bf8d3e2d3ad3f2f9d7bd9cf43ec6feaa64181775e60190153f2
      url: "https://pub.dev"
    source: hosted
    version: "0.12.17"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: f7142bb1154231d7ea5f96bc7bde4bda2a0945d2806bb11670e30b850d56bdec
      url: "https://pub.dev"
    source: hosted
    version: "0.11.1"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: e3641ec5d63ebf0d9b41bd43201a66e3fc79a65db5f61fc181f04cd27aab950c
      url: "https://pub.dev"
    source: hosted
    version: "1.16.0"
  meta_seo:
    dependency: transitive
    description:
      name: meta_seo
      sha256: fb1984710baca873da6ca9cd411d869bc56370f0cfb8e140dd42827424c681b5
      url: "https://pub.dev"
    source: hosted
    version: "3.0.9"
  mime:
    dependency: transitive
    description:
      name: mime
      sha256: e4ff8e8564c03f255408decd16e7899da1733852a9110a58fe6d1b817684a63e
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  multiple_localization:
    dependency: transitive
    description:
      path: "."
      ref: "48b65fa88ed468331d56ee86a102d808596c0a50"
      resolved-ref: "48b65fa88ed468331d56ee86a102d808596c0a50"
      url: "https://github.com/inspireui/flutter_multiple_localization"
    source: git
    version: "0.3.0"
  nested:
    dependency: transitive
    description:
      name: nested
      sha256: "03bac4c528c64c95c722ec99280375a6f2fc708eec17c7b3f07253b626cd2a20"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  new_version_plus:
    dependency: transitive
    description:
      name: new_version_plus
      sha256: e0d8027223488cc7f7c78f6ff286f1b5d9808f88814aecfa0bb12254d25f48ca
      url: "https://pub.dev"
    source: hosted
    version: "0.0.11"
  notification_permissions:
    dependency: transitive
    description:
      path: "../notification_permissions"
      relative: true
    source: path
    version: "0.6.1"
  octo_image:
    dependency: transitive
    description:
      name: octo_image
      sha256: "45b40f99622f11901238e18d48f5f12ea36426d8eced9f4cbf58479c7aa2430d"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  package_info_plus:
    dependency: transitive
    description:
      name: package_info_plus
      sha256: ceb027f6bc6a60674a233b4a90a7658af1aebdea833da0b5b53c1e9821a78c7b
      url: "https://pub.dev"
    source: hosted
    version: "4.0.2"
  package_info_plus_platform_interface:
    dependency: transitive
    description:
      name: package_info_plus_platform_interface
      sha256: "9bc8ba46813a4cc42c66ab781470711781940780fd8beddd0c3da62506d3a6c6"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  path:
    dependency: transitive
    description:
      name: path
      sha256: "75cca69d1490965be98c73ceaea117e8a04dd21217b37b292c9ddbec0d955bc5"
      url: "https://pub.dev"
    source: hosted
    version: "1.9.1"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      sha256: e3e67b1629e6f7e8100b367d3db6ba6af4b1f0bb80f64db18ef1fbabd2fa9ccf
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  path_provider:
    dependency: transitive
    description:
      name: path_provider
      sha256: "50c5dd5b6e1aaf6fb3a78b33f6aa3afca52bf903a8a5298f53101fdaee55bbcd"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.5"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: "0ca7359dad67fd7063cb2892ab0c0737b2daafd807cf1acecd62374c8fae6c12"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.16"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      sha256: "4843174df4d288f5e29185bd6e72a6fbdf5a4a4602717eed565497429f179942"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: ba2b77f0c52a33db09fc8caf85b12df691bf28d983e84cf87ff6d693cfa007b3
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: bced5679c7df11190e1ddc35f3222c858f328fff85c3942e46e7f5589bf9eb84
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: ee0e0d164516b90ae1f970bdf29f726f1aa730d7cfc449ecc74c495378b705da
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  paypal_payment:
    dependency: transitive
    description:
      name: paypal_payment
      sha256: "912ddc0fa0735fb9ff5001ef74ff8151036b6d402569a6c424cf1ea628683835"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  permission_handler:
    dependency: transitive
    description:
      name: permission_handler
      sha256: "59adad729136f01ea9e35a48f5d1395e25cba6cea552249ddbe9cf950f5d7849"
      url: "https://pub.dev"
    source: hosted
    version: "11.4.0"
  permission_handler_android:
    dependency: transitive
    description:
      name: permission_handler_android
      sha256: d3971dcdd76182a0c198c096b5db2f0884b0d4196723d21a866fc4cdea057ebc
      url: "https://pub.dev"
    source: hosted
    version: "12.1.0"
  permission_handler_apple:
    dependency: transitive
    description:
      name: permission_handler_apple
      sha256: f84a188e79a35c687c132a0a0556c254747a08561e99ab933f12f6ca71ef3c98
      url: "https://pub.dev"
    source: hosted
    version: "9.4.6"
  permission_handler_html:
    dependency: transitive
    description:
      name: permission_handler_html
      sha256: "54bf176b90f6eddd4ece307e2c06cf977fb3973719c35a93b85cc7093eb6070d"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.1"
  permission_handler_platform_interface:
    dependency: transitive
    description:
      name: permission_handler_platform_interface
      sha256: eb99b295153abce5d683cac8c02e22faab63e50679b937fa1bf67d58bb282878
      url: "https://pub.dev"
    source: hosted
    version: "4.3.0"
  permission_handler_windows:
    dependency: transitive
    description:
      name: permission_handler_windows
      sha256: "1a790728016f79a41216d88672dbc5df30e686e811ad4e698bfc51f76ad91f1e"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      sha256: cb3798bef7fc021ac45b308f4b51208a152792445cce0448c9a4ba5879dd8750
      url: "https://pub.dev"
    source: hosted
    version: "5.4.0"
  photo_manager:
    dependency: transitive
    description:
      name: photo_manager
      sha256: "32a1ce1095aeaaa792a29f28c1f74613aa75109f21c2d4ab85be3ad9964230a4"
      url: "https://pub.dev"
    source: hosted
    version: "3.5.0"
  photo_manager_image_provider:
    dependency: transitive
    description:
      name: photo_manager_image_provider
      sha256: b0a6d59f34e48c2b9aa52a4a655308c29b0fc4b3931607f9e0e252bc49aef974
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  pin_code_fields:
    dependency: transitive
    description:
      name: pin_code_fields
      sha256: "4c0db7fbc889e622e7c71ea54b9ee624bb70c7365b532abea0271b17ea75b729"
      url: "https://pub.dev"
    source: hosted
    version: "8.0.1"
  pin_input_text_field:
    dependency: transitive
    description:
      name: pin_input_text_field
      sha256: "712ac2dbf5098443ce90a2e1e6850654037a471bdfc9333b4e0a15e678642e39"
      url: "https://pub.dev"
    source: hosted
    version: "4.5.0"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "4a451831508d7d6ca779f7ac6e212b4023dd5a7d08a27a63da33756410e32b76"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: "4820fbfdb9478b1ebae27888254d445073732dae3d6ea81f0b7e06d5dedc3f02"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.8"
  pointycastle:
    dependency: transitive
    description:
      name: pointycastle
      sha256: "7c1e5f0d23c9016c5bbd8b1473d0d3fb3fc851b876046039509e18e0c7485f2c"
      url: "https://pub.dev"
    source: hosted
    version: "3.7.3"
  process:
    dependency: transitive
    description:
      name: process
      sha256: "53fd8db9cec1d37b0574e12f07520d582019cb6c44abf5479a01505099a34a09"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.4"
  provider:
    dependency: "direct main"
    description:
      name: provider
      sha256: c8a055ee5ce3fd98d6fc872478b03823ffdb448699c6ebdbbc71d59b596fd48c
      url: "https://pub.dev"
    source: hosted
    version: "6.1.2"
  pull_to_refresh:
    dependency: transitive
    description:
      path: "."
      ref: "24df296e282cd8b4ea8d3a5e171f4156a0295d1b"
      resolved-ref: "24df296e282cd8b4ea8d3a5e171f4156a0295d1b"
      url: "https://github.com/inspireui/flutter_pull_to_refresh"
    source: git
    version: "2.0.0"
  qr_code_scanner_plus:
    dependency: transitive
    description:
      name: qr_code_scanner_plus
      sha256: "39696b50d277097ee4d90d4292de36f38c66213a4f5216a06b2bdd2b63117859"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.10+1"
  quickalert:
    dependency: transitive
    description:
      path: "../quick_alert"
      relative: true
    source: path
    version: "1.1.0"
  quiver:
    dependency: transitive
    description:
      name: quiver
      sha256: b1c1ac5ce6688d77f65f3375a9abb9319b3cb32486bdc7a1e0fdf004d7ba4e47
      url: "https://pub.dev"
    source: hosted
    version: "3.2.1"
  random_string:
    dependency: transitive
    description:
      name: random_string
      sha256: "03b52435aae8cbdd1056cf91bfc5bf845e9706724dd35ae2e99fa14a1ef79d02"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1"
  rate_my_app:
    dependency: transitive
    description:
      name: rate_my_app
      sha256: e448dc27f8e821824fe7f67c2dba22f67b18de08054efa746defd1cd1657d882
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  readmore:
    dependency: transitive
    description:
      name: readmore
      sha256: "99c2483202f7c7e98c50834d72be2b119aefecf4497ca1960ae9d5f418eb1481"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  rect_getter:
    dependency: transitive
    description:
      name: rect_getter
      sha256: "1c71e4ab5fb7d83d2b14a8338f979bc9ae57eefba786e7fbc91f6746dac2193d"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  responsive_builder:
    dependency: transitive
    description:
      name: responsive_builder
      sha256: a38ba9ba86c9daf08904674553034b651377b1d685d10ee450d8350ae51f76ec
      url: "https://pub.dev"
    source: hosted
    version: "0.7.0"
  restart_app:
    dependency: transitive
    description:
      name: restart_app
      sha256: "00d5ec3e9de871cedbe552fc41e615b042b5ec654385e090e0983f6d02f655ed"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.2"
  rxdart:
    dependency: transitive
    description:
      name: rxdart
      sha256: "0c7c0cedd93788d996e33041ffecda924cc54389199cde4e6a34b440f50044cb"
      url: "https://pub.dev"
    source: hosted
    version: "0.27.7"
  sanitize_html:
    dependency: transitive
    description:
      name: sanitize_html
      sha256: "0a445f19bbaa196f5a4f93461aa066b94e6e025622eb1e9bc77872a5e25233a5"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  scroll_to_index:
    dependency: transitive
    description:
      name: scroll_to_index
      sha256: b707546e7500d9f070d63e5acf74fd437ec7eeeb68d3412ef7b0afada0b4f176
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  scrollable_positioned_list:
    dependency: transitive
    description:
      name: scrollable_positioned_list
      sha256: "1b54d5f1329a1e263269abc9e2543d90806131aa14fe7c6062a8054d57249287"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.8"
  shared_preferences:
    dependency: transitive
    description:
      name: shared_preferences
      sha256: "81429e4481e1ccfb51ede496e916348668fd0921627779233bd24cc3ff6abd02"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.2"
  shared_preferences_android:
    dependency: transitive
    description:
      name: shared_preferences_android
      sha256: "6478c6bbbecfe9aced34c483171e90d7c078f5883558b30ec3163cf18402c749"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  shared_preferences_foundation:
    dependency: transitive
    description:
      name: shared_preferences_foundation
      sha256: e014107bb79d6d3297196f4f2d0db54b5d1f85b8ea8ff63b8e8b391a02700feb
      url: "https://pub.dev"
    source: hosted
    version: "2.2.2"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      sha256: "9d387433ca65717bbf1be88f4d5bb18f10508917a8fa2fb02e0fd0d7479a9afa"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      sha256: "22e2ecac9419b4246d7c22bfbbda589e3acf5c0351137d87dd2939d984d37c3b"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      sha256: "74083203a8eae241e0de4a0d597dbedab3b8fef5563f33cf3c12d7e93c655ca5"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      sha256: "5e588e2efef56916a3b229c3bfe81e6a525665a454519ca51dbcc4236a274173"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  smooth_page_indicator:
    dependency: transitive
    description:
      name: smooth_page_indicator
      sha256: "725bc638d5e79df0c84658e1291449996943f93bacbc2cec49963dbbab48d8ae"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  sms_autofill:
    dependency: transitive
    description:
      name: sms_autofill
      sha256: "43139a175fb3c57ff103ac4b82f2aa70b6c45cf93539d7974c2556810f355363"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: "254ee5351d6cb365c859e20ee823c3bb479bf4a293c22d17a9f1bf144ce86f7c"
      url: "https://pub.dev"
    source: hosted
    version: "1.10.1"
  sprintf:
    dependency: transitive
    description:
      name: sprintf
      sha256: "1fc9ffe69d4df602376b52949af107d8f5703b77cda567c4d7d86a0693120f23"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0"
  sqflite:
    dependency: transitive
    description:
      name: sqflite
      sha256: b4d6710e1200e96845747e37338ea8a819a12b51689a3bcf31eff0003b37a0b9
      url: "https://pub.dev"
    source: hosted
    version: "2.2.8+4"
  sqflite_common:
    dependency: transitive
    description:
      name: sqflite_common
      sha256: "8f7603f3f8f126740bc55c4ca2d1027aab4b74a1267a3e31ce51fe40e3b65b8f"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.5+1"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: "8b27215b45d22309b5cddda1aa2b19bdfec9df0e765f2de506401c071d38d1b1"
      url: "https://pub.dev"
    source: hosted
    version: "1.12.1"
  sticky_headers:
    dependency: transitive
    description:
      name: sticky_headers
      sha256: "9b3dd2cb0fd6a7038170af3261f855660cbb241cb56c501452cb8deed7023ede"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.0+2"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: "969e04c80b8bcdf826f8f16579c7b14d780458bd97f56d107d3950fdbeef059d"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  stream_transform:
    dependency: transitive
    description:
      name: stream_transform
      sha256: "14a00e794c7c11aa145a170587321aedce29769c08d7f58b1d141da75e3b1c6f"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "921cd31725b72fe181906c6a94d987c78e3b98c2e205b397ea399d4054872b43"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.1"
  synchronized:
    dependency: transitive
    description:
      name: synchronized
      sha256: "5fcbd27688af6082f5abd611af56ee575342c30e87541d0245f7ff99faa02c60"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: "7f554798625ea768a7518313e58f83891c7f5024f88e46e7182a4558850a4b8e"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.2"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: fb31f383e2ee25fbbfe06b40fe21e1e458d14080e3c67e7ba0acfde4df4e0bbd
      url: "https://pub.dev"
    source: hosted
    version: "0.7.4"
  the_apple_sign_in:
    dependency: transitive
    description:
      name: the_apple_sign_in
      sha256: "52163df2619e5461f63559002eee171fe78ebab3af6f6f34c4026df5f15859ad"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  timeago:
    dependency: "direct main"
    description:
      name: timeago
      sha256: "054cedf68706bb142839ba0ae6b135f6b68039f0b8301cbe8784ae653d5ff8de"
      url: "https://pub.dev"
    source: hosted
    version: "3.7.0"
  timezone:
    dependency: transitive
    description:
      name: timezone
      sha256: "1cfd8ddc2d1cfd836bc93e67b9be88c3adaeca6f40a00ca999104c30693cdca0"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.2"
  translator:
    dependency: transitive
    description:
      name: translator
      sha256: "8f5e56d0ffb8f493b23ad0e4f824c17e5f43d45997e33b7c7b689c7a33cf3b06"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3+1"
  transparent_image:
    dependency: transitive
    description:
      name: transparent_image
      sha256: e8991d955a2094e197ca24c645efec2faf4285772a4746126ca12875e54ca02f
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: facc8d6582f16042dd49f2463ff1bd6e2c9ef9f3d5da3d9b087e244a7b564b3c
      url: "https://pub.dev"
    source: hosted
    version: "1.3.2"
  universal_html:
    dependency: transitive
    description:
      name: universal_html
      sha256: "56536254004e24d9d8cfdb7dbbf09b74cf8df96729f38a2f5c238163e3d58971"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.4"
  universal_io:
    dependency: transitive
    description:
      name: universal_io
      sha256: "1722b2dcc462b4b2f3ee7d188dad008b6eb4c40bbd03a3de451d82c78bba9aad"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.2"
  universal_platform:
    dependency: transitive
    description:
      name: universal_platform
      sha256: "64e16458a0ea9b99260ceb5467a214c1f298d647c659af1bff6d3bf82536b1ec"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  url_launcher:
    dependency: transitive
    description:
      name: url_launcher
      sha256: "9d06212b1362abc2f0f0d78e6f09f726608c74e3b9462e8368bb03314aa8d603"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.1"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      sha256: d4ed0711849dd8e33eb2dd69c25db0d0d3fdc37e0a62e629fe32f57a22db2745
      url: "https://pub.dev"
    source: hosted
    version: "6.3.0"
  url_launcher_ios:
    dependency: "direct main"
    description:
      name: url_launcher_ios
      sha256: "16a513b6c12bb419304e72ea0ae2ab4fed569920d1c7cb850263fe3acc824626"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.2"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      sha256: ab360eb661f8879369acac07b6bb3ff09d9471155357da8443fd5d3cf7363811
      url: "https://pub.dev"
    source: hosted
    version: "3.1.1"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      sha256: b7244901ea3cf489c5335bdacda07264a6e960b1c1b1a9f91e4bc371d9e68234
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      sha256: "552f8a1e663569be95a8190206a38187b531910283c3e982193e4f2733f01029"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      sha256: "772638d3b34c779ede05ba3d38af34657a05ac55b06279ea6edd409e323dca8e"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.3"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      sha256: "44cf3aabcedde30f2dba119a9dea3b0f2672fbe6fa96e85536251d678216b3c4"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.3"
  uuid:
    dependency: transitive
    description:
      name: uuid
      sha256: cd210a09f7c18cbe5a02511718e0334de6559871052c90a90c0cca46a4aa81c8
      url: "https://pub.dev"
    source: hosted
    version: "4.3.3"
  vector_graphics:
    dependency: transitive
    description:
      name: vector_graphics
      sha256: "44cc7104ff32563122a929e4620cf3efd584194eec6d1d913eb5ba593dbcf6de"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.18"
  vector_graphics_codec:
    dependency: transitive
    description:
      name: vector_graphics_codec
      sha256: c86987475f162fadff579e7320c7ddda04cd2fdeffbe1129227a85d9ac9e03da
      url: "https://pub.dev"
    source: hosted
    version: "1.1.11+1"
  vector_graphics_compiler:
    dependency: transitive
    description:
      name: vector_graphics_compiler
      sha256: "1b4b9e706a10294258727674a340ae0d6e64a7231980f9f9a3d12e4b42407aad"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.16"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  video_player:
    dependency: transitive
    description:
      name: video_player
      sha256: "48941c8b05732f9582116b1c01850b74dbee1d8520cd7e34ad4609d6df666845"
      url: "https://pub.dev"
    source: hosted
    version: "2.9.3"
  video_player_android:
    dependency: transitive
    description:
      name: video_player_android
      sha256: f338a5a396c845f4632959511cad3542cdf3167e1b2a1a948ef07f7123c03608
      url: "https://pub.dev"
    source: hosted
    version: "2.4.9"
  video_player_avfoundation:
    dependency: transitive
    description:
      name: video_player_avfoundation
      sha256: "309e3962795e761be010869bae65c0b0e45b5230c5cee1bec72197ca7db040ed"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.6"
  video_player_platform_interface:
    dependency: transitive
    description:
      name: video_player_platform_interface
      sha256: df534476c341ab2c6a835078066fc681b8265048addd853a1e3c78740316a844
      url: "https://pub.dev"
    source: hosted
    version: "6.3.0"
  video_player_web:
    dependency: transitive
    description:
      name: video_player_web
      sha256: "3ef40ea6d72434edbfdba4624b90fd3a80a0740d260667d91e7ecd2d79e13476"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.4"
  visibility_detector:
    dependency: transitive
    description:
      name: visibility_detector
      sha256: dd5cc11e13494f432d15939c3aa8ae76844c42b723398643ce9addb88a5ed420
      url: "https://pub.dev"
    source: hosted
    version: "0.4.0+2"
  vm_service:
    dependency: transitive
    description:
      name: vm_service
      sha256: ddfa8d30d89985b96407efce8acbdd124701f96741f2d981ca860662f1c0dc02
      url: "https://pub.dev"
    source: hosted
    version: "15.0.0"
  web:
    dependency: transitive
    description:
      name: web
      sha256: cd3543bd5798f6ad290ea73d210f423502e71900302dde696f8bff84bf89a1cb
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  webview_flutter:
    dependency: transitive
    description:
      name: webview_flutter
      sha256: "889a0a678e7c793c308c68739996227c9661590605e70b1f6cf6b9a6634f7aec"
      url: "https://pub.dev"
    source: hosted
    version: "4.10.0"
  webview_flutter_android:
    dependency: transitive
    description:
      name: webview_flutter_android
      sha256: e09150b28a07933839adef0e4a088bb43e8c8d9e6b93025b01882d4067a58ab0
      url: "https://pub.dev"
    source: hosted
    version: "4.3.4"
  webview_flutter_platform_interface:
    dependency: transitive
    description:
      name: webview_flutter_platform_interface
      sha256: d937581d6e558908d7ae3dc1989c4f87b786891ab47bb9df7de548a151779d8d
      url: "https://pub.dev"
    source: hosted
    version: "2.10.0"
  webview_flutter_web:
    dependency: transitive
    description:
      name: webview_flutter_web
      sha256: cbe1efe45e1be8470fdef7ddb75e2e2998c7ca47b75c09b9354934d20eca146b
      url: "https://pub.dev"
    source: hosted
    version: "0.2.3+2"
  webview_flutter_wkwebview:
    dependency: transitive
    description:
      name: webview_flutter_wkwebview
      sha256: c14455137ce60a68e1ccaf4e8f2dae8cebcb3465ddaa2fcfb57584fb7c5afe4d
      url: "https://pub.dev"
    source: hosted
    version: "3.18.5"
  wechat_assets_picker:
    dependency: transitive
    description:
      name: wechat_assets_picker
      sha256: a7ca044e5fb4eae1b83ef4fad81d19c7612afe062192482eb164885e4289ba37
      url: "https://pub.dev"
    source: hosted
    version: "9.3.1"
  wechat_picker_library:
    dependency: transitive
    description:
      name: wechat_picker_library
      sha256: a42e09cb85b15fc9410f6a69671371cc60aa99c4a1f7967f6593a7f665f6f47a
      url: "https://pub.dev"
    source: hosted
    version: "1.0.5"
  win32:
    dependency: transitive
    description:
      name: win32
      sha256: "84ba388638ed7a8cb3445a320c8273136ab2631cd5f2c57888335504ddab1bc2"
      url: "https://pub.dev"
    source: hosted
    version: "5.8.0"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: ee1505df1426458f7f60aac270645098d318a8b4766d85fde75f76f2e21807d1
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  xml:
    dependency: transitive
    description:
      name: xml
      sha256: "5bc72e1e45e941d825fd7468b9b4cc3b9327942649aeb6fc5cdbf135f0a86e84"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.0"
  youtube_player_iframe:
    dependency: "direct overridden"
    description:
      name: youtube_player_iframe
      sha256: "66020f7756accfb22b3297565d845f9bef14249c730dd51e1ec648fa155fb24a"
      url: "https://pub.dev"
    source: hosted
    version: "5.2.1"
  youtube_player_iframe_web:
    dependency: transitive
    description:
      name: youtube_player_iframe_web
      sha256: "05222a228937932e7ee7a6171e8020fee4cd23d1c7bf6b4128c569484338c593"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.1"
sdks:
  dart: ">=3.7.0 <4.0.0"
  flutter: ">=3.27.0"
